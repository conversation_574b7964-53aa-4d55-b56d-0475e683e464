

console.log('Loading Veritas Multi-Agent System (Enhanced Discussion Framework)...');

// Discussion Message Types
const MessageType = {
    PROPOSAL: 'proposal',
    QUESTION: 'question',
    RESPONSE: 'response',
    CHALLENGE: 'challenge',
    AGREEMENT: 'agreement',
    SYNTHESIS: 'synthesis'
};

// Agent Personality Traits
const PersonalityTraits = {
    ANALYTICAL: 'analytical',
    CREATIVE: 'creative',
    DETAIL_ORIENTED: 'detail_oriented',
    BIG_PICTURE: 'big_picture',
    DIPLOMATIC: 'diplomatic',
    DIRECT: 'direct',
    CURIOUS: 'curious',
    PRACTICAL: 'practical'
};

// Chain of Thought Communication System
class ChainOfThought {
    constructor(originalRequest, participants = []) {
        this.id = this.generateChainId();
        this.originalRequest = originalRequest;
        this.participants = participants;
        this.chain = [];
        this.currentStep = 0;
        this.sharedContext = {
            requirements: {},
            constraints: {},
            solutions: {},
            decisions: {},
            nextSteps: {}
        };
        this.startTime = new Date();
    }

    generateChainId() {
        return 'chain_' + Math.random().toString(36).substring(2, 11);
    }

    // Add a step to the chain with detailed context passing and collaboration tracking
    addStep(agent, stepType, content, outputData = {}) {
        const step = {
            stepNumber: this.currentStep + 1,
            agent: agent,
            stepType: stepType, // 'analysis', 'planning', 'implementation', 'research', 'synthesis'
            content: content,
            inputContext: JSON.parse(JSON.stringify(this.sharedContext)), // Deep copy of context at this point
            outputData: outputData,
            timestamp: new Date(),
            nextAgent: null,
            collaborationInsights: this.extractCollaborationInsights(content),
            agentTags: this.extractAgentTags(content),
            reasoning: outputData.reasoning || ''
        };

        // Update shared context with new information
        this.updateSharedContext(agent, outputData);

        // Track collaboration patterns
        this.trackCollaboration(agent, step);

        this.chain.push(step);
        this.currentStep++;

        return step;
    }

    // Extract collaboration insights from agent responses
    extractCollaborationInsights(content) {
        const insights = {
            buildsUpon: [],
            references: [],
            identifies: [],
            suggests: []
        };

        // Look for collaboration patterns
        const buildPatterns = [
            /building upon|builds on|expanding on|based on.*from/gi,
            /as.*mentioned|as.*noted|following up on/gi
        ];

        const referencePatterns = [
            /@(\w+)|the (\w+) agent|our (\w+) specialist/gi
        ];

        const identifyPatterns = [
            /identifies|recognizes|notes that|observes/gi
        ];

        const suggestPatterns = [
            /suggests|recommends|proposes|should consider/gi
        ];

        buildPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) insights.buildsUpon.push(...matches);
        });

        referencePatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) insights.references.push(...matches);
        });

        identifyPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) insights.identifies.push(...matches);
        });

        suggestPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) insights.suggests.push(...matches);
        });

        return insights;
    }

    // Extract agent tags from content
    extractAgentTags(content) {
        const tags = [];
        const tagPattern = /@(\w+)\s*([^@]+?)(?=@\w+|$)/gs;
        let match;

        while ((match = tagPattern.exec(content)) !== null) {
            tags.push({
                agent: match[1].toLowerCase(),
                message: match[2].trim(),
                timestamp: new Date()
            });
        }

        return tags;
    }

    // Track collaboration patterns between agents
    trackCollaboration(currentAgent, step) {
        if (!this.sharedContext.collaboration) {
            this.sharedContext.collaboration = {
                patterns: [],
                agentInteractions: {},
                knowledgeFlow: []
            };
        }

        // Track knowledge flow
        if (step.collaborationInsights.references.length > 0) {
            this.sharedContext.collaboration.knowledgeFlow.push({
                from: step.collaborationInsights.references,
                to: currentAgent,
                step: step.stepNumber,
                type: 'reference'
            });
        }

        // Track agent interactions
        if (!this.sharedContext.collaboration.agentInteractions[currentAgent]) {
            this.sharedContext.collaboration.agentInteractions[currentAgent] = {
                stepCount: 0,
                collaborations: [],
                contributions: []
            };
        }

        this.sharedContext.collaboration.agentInteractions[currentAgent].stepCount++;
        this.sharedContext.collaboration.agentInteractions[currentAgent].contributions.push({
            step: step.stepNumber,
            type: step.stepType,
            insights: step.collaborationInsights
        });
    }

    // Update the shared context that gets passed between agents
    updateSharedContext(agent, outputData) {
        if (outputData.requirements) {
            this.sharedContext.requirements[agent] = outputData.requirements;
        }
        if (outputData.constraints) {
            this.sharedContext.constraints[agent] = outputData.constraints;
        }
        if (outputData.solutions) {
            this.sharedContext.solutions[agent] = outputData.solutions;
        }
        if (outputData.decisions) {
            this.sharedContext.decisions[agent] = outputData.decisions;
        }
        if (outputData.nextSteps) {
            this.sharedContext.nextSteps[agent] = outputData.nextSteps;
        }
    }

    // Get the complete context for the next agent
    getContextForAgent(agentName) {
        return {
            originalRequest: this.originalRequest,
            chainHistory: this.chain,
            sharedContext: this.sharedContext,
            previousSteps: this.chain.map(step => ({
                agent: step.agent,
                stepType: step.stepType,
                summary: step.content.substring(0, 200) + '...',
                keyOutputs: step.outputData
            })),
            currentStep: this.currentStep,
            nextAgent: agentName
        };
    }

    // Determine the logical next agent in the chain
    getNextAgent(currentAgent, requestType = 'general') {
        const chainSequences = {
            'business': ['orchestrator', 'research', 'planner', 'finance', 'marketing', 'sales', 'operations', 'legal', 'hr', 'consulting', 'coding', 'synthesis'],
            'technical': ['orchestrator', 'coding', 'data', 'ai', 'devops', 'security', 'research', 'planner', 'operations', 'synthesis'],
            'research': ['orchestrator', 'research', 'data', 'ai', 'planner', 'coding', 'operations', 'synthesis'],
            'creative': ['orchestrator', 'creative', 'design', 'marketing', 'communications', 'research', 'planner', 'synthesis'],
            'healthcare': ['orchestrator', 'healthcare', 'research', 'data', 'legal', 'operations', 'planner', 'synthesis'],
            'education': ['orchestrator', 'education', 'research', 'creative', 'design', 'planner', 'operations', 'synthesis'],
            'general': ['orchestrator', 'solver', 'planner', 'research', 'coding', 'data', 'ai', 'creative', 'operations', 'synthesis']
        };

        const sequence = chainSequences[requestType] || chainSequences['general'];
        const currentIndex = sequence.indexOf(currentAgent);

        if (currentIndex >= 0 && currentIndex < sequence.length - 1) {
            return sequence[currentIndex + 1];
        }

        return null; // End of chain
    }

    // Get a summary of the entire chain for final synthesis
    getChainSummary() {
        return {
            totalSteps: this.chain.length,
            participants: [...new Set(this.chain.map(step => step.agent))],
            keyDecisions: this.sharedContext.decisions,
            finalSolutions: this.sharedContext.solutions,
            implementationSteps: this.sharedContext.nextSteps,
            duration: new Date() - this.startTime
        };
    }
}

// Discussion Management Class
class Discussion {
    constructor(originalRequest, participants = []) {
        this.id = this.generateDiscussionId();
        this.originalRequest = originalRequest;
        this.participants = participants;
        this.messages = [];
        this.currentSpeaker = null;
        this.status = 'active'; // active, consensus, completed
        this.startTime = new Date();
        this.maxRounds = 20; // Increased maximum discussion rounds for comprehensive collaboration
        this.currentRound = 0;
    }

    generateDiscussionId() {
        return 'disc_' + Math.random().toString(36).substr(2, 9);
    }

    addMessage(message) {
        // Evaluate message quality before adding
        const quality = this.evaluateMessageQuality(message.content, {
            originalRequest: this.originalRequest,
            previousMessages: this.messages.map(m => m.content)
        });

        // Add quality metadata to message
        message.quality = quality;

        // Log quality issues if any
        if (quality.issues.length > 0) {
            console.log(`⚠️ Message quality issues for ${message.agent}:`, quality.issues);
        }

        // Only add message if quality is acceptable
        if (quality.isAcceptable) {
            this.messages.push(message);
            this.currentSpeaker = message.agent;

            // Log the discussion message to console
            console.log(`🤖 ${message.agent.toUpperCase()} AGENT DISCUSSION MESSAGE:`);
            console.log(`💬 Content: ${message.content.substring(0, 500)}${message.content.length > 500 ? '...' : ''}`);
            console.log(`🏷️ Type: ${message.type || 'discussion'}`);
            console.log(`⏰ Timestamp: ${new Date().toLocaleTimeString()}`);
            console.log('─'.repeat(80));
        } else {
            console.log(`❌ Message from ${message.agent} rejected due to poor quality (score: ${quality.score})`);
            // Could implement retry logic here
        }
    }

    // Message quality filtering system (moved from BaseAgent to Discussion for context)
    evaluateMessageQuality(message, context = {}) {
        const quality = {
            score: 0,
            issues: [],
            recommendations: [],
            isAcceptable: false
        };

        // Check message length and substance
        if (message.length < 20) {
            quality.issues.push('Message too short');
            quality.score -= 2;
        } else if (message.length > 2000) {
            quality.issues.push('Message too long');
            quality.score -= 1;
        } else {
            quality.score += 1;
        }

        // Check for meaningful content
        const meaningfulWords = message.split(/\s+/).filter(word =>
            word.length > 3 &&
            !['this', 'that', 'with', 'from', 'they', 'have', 'will', 'been', 'were'].includes(word.toLowerCase())
        );

        if (meaningfulWords.length < 5) {
            quality.issues.push('Lacks meaningful content');
            quality.score -= 2;
        } else {
            quality.score += 1;
        }

        // Check for specific, actionable information
        const actionablePatterns = [
            /\b(recommend|suggest|propose|should|could|would|need to|plan to|will)\b/gi,
            /\b(analyze|research|develop|create|implement|design|build)\b/gi,
            /\b(because|since|due to|as a result|therefore|consequently)\b/gi
        ];

        let actionableCount = 0;
        actionablePatterns.forEach(pattern => {
            const matches = message.match(pattern);
            if (matches) actionableCount += matches.length;
        });

        if (actionableCount >= 2) {
            quality.score += 2;
        } else if (actionableCount >= 1) {
            quality.score += 1;
        } else {
            quality.issues.push('Lacks actionable insights');
            quality.score -= 1;
        }

        // Check for vague or generic responses
        const vaguePhrases = [
            'it depends', 'maybe', 'possibly', 'might be', 'could be',
            'generally speaking', 'in most cases', 'typically',
            'i think', 'i believe', 'in my opinion'
        ];

        let vagueCount = 0;
        vaguePhrases.forEach(phrase => {
            if (message.toLowerCase().includes(phrase)) vagueCount++;
        });

        if (vagueCount > 2) {
            quality.issues.push('Too many vague statements');
            quality.score -= 2;
        }

        // Check for redundancy with previous messages
        if (context.previousMessages && context.previousMessages.length > 0) {
            const similarity = this.calculateMessageSimilarity(message, context.previousMessages);
            if (similarity > 0.7) {
                quality.issues.push('Highly redundant with previous messages');
                quality.score -= 3;
            } else if (similarity > 0.5) {
                quality.issues.push('Some redundancy with previous messages');
                quality.score -= 1;
            }
        }

        // Generate recommendations
        if (quality.issues.includes('Message too short')) {
            quality.recommendations.push('Provide more detailed explanation');
        }
        if (quality.issues.includes('Lacks actionable insights')) {
            quality.recommendations.push('Include specific recommendations or next steps');
        }

        // Determine if message is acceptable (threshold: 0 or higher)
        quality.isAcceptable = quality.score >= 0;

        return quality;
    }

    // Calculate similarity between a message and previous messages
    calculateMessageSimilarity(newMessage, previousMessages) {
        if (!previousMessages || previousMessages.length === 0) return 0;

        const newWords = new Set(newMessage.toLowerCase().split(/\s+/).filter(word => word.length > 3));
        let maxSimilarity = 0;

        for (const prevMessage of previousMessages) {
            const prevWords = new Set(prevMessage.toLowerCase().split(/\s+/).filter(word => word.length > 3));
            const intersection = new Set([...newWords].filter(word => prevWords.has(word)));
            const union = new Set([...newWords, ...prevWords]);

            const similarity = union.size > 0 ? intersection.size / union.size : 0;
            maxSimilarity = Math.max(maxSimilarity, similarity);
        }

        return maxSimilarity;
    }

    getLastMessage() {
        return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
    }

    getMessagesByAgent(agentName) {
        return this.messages.filter(msg => msg.agent === agentName);
    }

    hasAgentSpoken(agentName) {
        return this.messages.some(msg => msg.agent === agentName);
    }

    getDiscussionSummary() {
        const summary = {
            totalMessages: this.messages.length,
            participantCount: new Set(this.messages.map(msg => msg.agent)).size,
            messageTypes: {},
            keyPoints: []
        };

        // Count message types
        this.messages.forEach(msg => {
            summary.messageTypes[msg.type] = (summary.messageTypes[msg.type] || 0) + 1;
        });

        return summary;
    }

    shouldContinueDiscussion() {
        // Continue if we haven't reached max rounds and there's active discussion
        if (this.currentRound >= this.maxRounds) return false;
        if (this.status !== 'active') return false;

        // Check if we have meaningful participation
        const uniqueSpeakers = new Set(this.messages.map(msg => msg.agent)).size;
        return uniqueSpeakers < this.participants.length || this.messages.length < 3;
    }

    getNextSpeaker(currentAgent) {
        // Determine who should speak next based on discussion flow
        const lastMessage = this.getLastMessage();

        if (!lastMessage) {
            return this.participants[0]; // Start with first participant
        }

        // If someone was mentioned, they should respond
        if (lastMessage.mentions && lastMessage.mentions.length > 0) {
            const mentionedAgent = lastMessage.mentions[0];
            if (this.participants.includes(mentionedAgent) && mentionedAgent !== currentAgent) {
                return mentionedAgent;
            }
        }

        // If it's a question, route to most relevant expert
        if (lastMessage.type === MessageType.QUESTION) {
            return this.findMostRelevantAgent(lastMessage.content);
        }

        // Otherwise, rotate to next participant who hasn't spoken recently
        const recentSpeakers = this.messages.slice(-3).map(msg => msg.agent);
        const availableSpeakers = this.participants.filter(agent =>
            agent !== currentAgent && !recentSpeakers.includes(agent)
        );

        return availableSpeakers.length > 0 ? availableSpeakers[0] : this.participants[0];
    }

    findMostRelevantAgent(content) {
        // Simple rotation to next available participant
        return this.participants[0];
    }
}

// CommunicationCoordinator removed - using AI intelligence instead of hardcoded logic

// Enhanced Base Agent Class with Discussion Capabilities
class BaseAgent {
    constructor(name, role, capabilities = [], personality = {}) {
        this.name = name;
        this.role = role;
        this.capabilities = capabilities;
        this.personality = personality;
        this.conversationHistory = [];
        this.collaborators = new Map();
        this.discussionMemory = [];
        this.currentDiscussion = null;
        // CommunicationCoordinator removed - using AI intelligence instead
    }

    // Core method for processing requests
    async processRequest(request, context = {}) {
        throw new Error('processRequest must be implemented by subclass');
    }

    // Enhanced discussion-based communication using AI intelligence
    async participateInDiscussion(discussion, context = {}) {
        // Let AI naturally determine participation value instead of hardcoded logic
        console.log(`✅ ${this.name} participating with AI-driven intelligence`);

        const prompt = this.generateDiscussionPrompt(discussion, context);

        // Use AI intelligence to determine participation value
        const enhancedPrompt = `${prompt}

PARTICIPATION CONTEXT: Use your AI intelligence to determine if and how you can contribute meaningfully to this discussion.
Focus on your specific expertise and provide value that complements other agents' contributions.
Be specific, actionable, and directly relevant to the main topic.
If you don't have meaningful input to add, provide a brief acknowledgment instead of a full response.`;

        const response = await this.callGeminiAPI(enhancedPrompt, context);

        // Parse the response for discussion elements
        const discussionMessage = this.parseDiscussionResponse(response);

        // Validate response quality before returning
        const quality = discussion.evaluateMessageQuality(discussionMessage.content, {
            originalRequest: discussion.originalRequest,
            previousMessages: discussion.messages.map(m => m.content)
        });

        if (!quality.isAcceptable) {
            console.log(`❌ ${this.name} response rejected due to quality issues:`, quality.issues);
            return null; // Don't participate if response quality is poor
        }

        // Add quality and participation metadata
        discussionMessage.quality = quality;
        discussionMessage.participationReasoning = participationDecision.reasoning;

        // Add to discussion memory
        this.addToDiscussionMemory(discussionMessage);

        return discussionMessage;
    }

    // Generate discussion-specific prompts
    generateDiscussionPrompt(discussion, context = {}) {
        let prompt = `You are ${this.role} participating in a collaborative discussion to solve: "${discussion.originalRequest}"\n\n`;

        // Add personality context
        prompt += `Your personality traits: ${Object.values(this.personality).join(', ')}\n`;
        prompt += `Your expertise: ${this.capabilities.join(', ')}\n\n`;

        // Add discussion history
        if (discussion.messages.length > 0) {
            prompt += "Discussion so far:\n";
            discussion.messages.forEach((msg, idx) => {
                prompt += `${idx + 1}. ${msg.agent} (${msg.type}): ${msg.content}\n`;
            });
            prompt += '\n';
        }

        // Add conversation context
        if (context.conversationHistory && context.conversationHistory.length > 0) {
            prompt += "Previous conversation context:\n";
            const recentHistory = context.conversationHistory.slice(-5);
            recentHistory.forEach((msg, idx) => {
                prompt += `${idx + 1}. ${msg.role}: ${msg.content.substring(0, 150)}...\n`;
            });
            prompt += '\n';
        }

        // Add discussion guidelines
        prompt += this.getDiscussionGuidelines(discussion);

        return prompt;
    }

    // Get discussion guidelines based on agent role and discussion state
    getDiscussionGuidelines(discussion) {
        const guidelines = `
Discussion Guidelines:
- Engage naturally and conversationally with other agents
- Ask clarifying questions when you need more information
- Build upon others' ideas and provide constructive feedback
- Challenge assumptions respectfully when necessary
- Reference previous points in the discussion using agent names
- Stay focused on your area of expertise while being collaborative
- Use @agent_name to directly address specific agents
- Indicate your message type: [PROPOSAL], [QUESTION], [RESPONSE], [CHALLENGE], [AGREEMENT]

Your response should be natural and conversational, as if you're in a meeting with colleagues.
Start your response with the appropriate message type in brackets.
`;
        return guidelines;
    }

    // Parse discussion response to extract message type and content
    parseDiscussionResponse(response) {
        const messageTypeRegex = /\[(PROPOSAL|QUESTION|RESPONSE|CHALLENGE|AGREEMENT|SYNTHESIS)\]/i;
        const match = response.match(messageTypeRegex);

        const messageType = match ? match[1].toLowerCase() : MessageType.RESPONSE;
        const content = response.replace(messageTypeRegex, '').trim();

        // Extract mentions of other agents
        const mentions = this.extractMentions(content);

        return {
            agent: this.name,
            type: messageType,
            content: content,
            mentions: mentions,
            timestamp: new Date()
        };
    }

    // Extract @agent_name mentions from content
    extractMentions(content) {
        const mentionRegex = /@(\w+)/g;
        const mentions = [];
        let match;

        while ((match = mentionRegex.exec(content)) !== null) {
            mentions.push(match[1].toLowerCase());
        }

        return mentions;
    }

    // Add message to discussion memory
    addToDiscussionMemory(message) {
        this.discussionMemory.push(message);

        // Keep only last 20 messages to prevent memory overflow
        if (this.discussionMemory.length > 20) {
            this.discussionMemory = this.discussionMemory.slice(-20);
        }
    }

    // Enhanced chain-of-thought processing
    async processChainStep(chainContext, stepType = 'analysis') {
        const prompt = this.generateChainPrompt(chainContext, stepType);
        const response = await this.callGeminiAPI(prompt, chainContext);

        // Parse the response to extract structured output
        const structuredOutput = this.parseChainResponse(response, stepType);

        return {
            content: response,
            structuredData: structuredOutput,
            stepType: stepType,
            agent: this.name
        };
    }

    // Generate chain-specific prompts with context passing
    generateChainPrompt(chainContext, stepType) {
        let prompt = `You are ${this.role} working as part of a collaborative team to solve: "${chainContext.originalRequest}"\n\n`;

        // Add personality and expertise context
        prompt += `Your expertise: ${this.capabilities.join(', ')}\n`;
        prompt += `Your approach: ${this.personality.communicationStyle}\n\n`;

        // Add chain history and context
        if (chainContext.previousSteps && chainContext.previousSteps.length > 0) {
            prompt += "PREVIOUS TEAM WORK:\n";
            chainContext.previousSteps.forEach((step, idx) => {
                prompt += `${idx + 1}. ${step.agent} (${step.stepType}): ${step.summary}\n`;
                if (step.keyOutputs && Object.keys(step.keyOutputs).length > 0) {
                    prompt += `   Key outputs: ${JSON.stringify(step.keyOutputs, null, 2)}\n`;
                }
            });
            prompt += '\n';
        }

        // Add shared context
        if (chainContext.sharedContext) {
            prompt += "SHARED TEAM KNOWLEDGE:\n";
            Object.entries(chainContext.sharedContext).forEach(([category, data]) => {
                if (Object.keys(data).length > 0) {
                    prompt += `${category.toUpperCase()}: ${JSON.stringify(data, null, 2)}\n`;
                }
            });
            prompt += '\n';
        }

        // Add step-specific instructions
        prompt += this.getStepInstructions(stepType, chainContext);

        return prompt;
    }

    // Get step-specific instructions based on agent role and step type
    getStepInstructions(stepType, chainContext) {
        const baseInstructions = `
YOUR TASK (${stepType.toUpperCase()}):
Build upon the previous team members' work and contribute your specialized expertise.

RESPONSE FORMAT:
1. First, acknowledge what previous team members have established
2. Provide your specialized analysis/contribution
3. Identify any gaps or concerns from your perspective
4. Make specific recommendations for the next team member
5. End with structured output in this EXACT format:

STRUCTURED_OUTPUT:
{
  "requirements": {"key1": "value1", "key2": "value2"},
  "constraints": {"key1": "value1", "key2": "value2"},
  "solutions": {"key1": "value1", "key2": "value2"},
  "decisions": {"key1": "value1", "key2": "value2"},
  "nextSteps": {"key1": "value1", "key2": "value2"}
}

CRITICAL JSON RULES:
- Use double quotes for all keys and string values
- No trailing commas
- No line breaks within string values
- Keep it simple with string values only
- Ensure valid JSON syntax

Be specific, detailed, and ensure your work builds meaningfully on what came before.
`;

        const roleSpecificInstructions = {
            'orchestrator': 'Focus on overall coordination, identifying what each specialist needs to contribute, and ensuring all aspects are covered.',
            'planner': 'Analyze business requirements, create strategic roadmaps, identify resources and timelines, assess risks.',
            'coding': 'Evaluate technical feasibility, design architecture, identify implementation challenges, recommend technologies.',
            'research': 'Gather relevant data, analyze market conditions, validate assumptions, provide evidence-based insights.',
            'synthesis': 'Integrate all team inputs into a comprehensive solution, resolve conflicts, create actionable recommendations.'
        };

        return baseInstructions + '\n' + (roleSpecificInstructions[this.name] || '');
    }

    // Parse structured output from agent responses
    parseChainResponse(response, stepType) {
        const structuredOutput = {
            requirements: {},
            constraints: {},
            solutions: {},
            decisions: {},
            nextSteps: {}
        };

        // Try to extract structured output with multiple patterns
        const patterns = [
            /STRUCTURED_OUTPUT:\s*({[\s\S]*?})\s*(?:\n|$)/,
            /STRUCTURED_OUTPUT:\s*({[\s\S]*})$/,
            /{[\s\S]*"requirements"[\s\S]*}/,
            /{[\s\S]*"solutions"[\s\S]*}/
        ];

        let jsonString = null;
        for (const pattern of patterns) {
            const match = response.match(pattern);
            if (match) {
                jsonString = match[1] || match[0];
                break;
            }
        }

        if (jsonString) {
            try {
                // Clean up the JSON string
                jsonString = this.cleanJsonString(jsonString);
                const parsed = JSON.parse(jsonString);
                Object.assign(structuredOutput, parsed);
                console.log('✅ Successfully parsed structured output:', parsed);
            } catch (e) {
                console.warn('❌ Failed to parse structured output:', e);
                console.warn('Raw JSON string:', jsonString);
                // Try to extract partial information
                this.extractPartialJson(jsonString, structuredOutput);
            }
        }

        // Fallback: extract key information using patterns
        if (Object.keys(structuredOutput.requirements).length === 0 &&
            Object.keys(structuredOutput.solutions).length === 0) {
            this.extractKeyInformation(response, structuredOutput);
        }

        return structuredOutput;
    }

    // Clean up JSON string to fix common formatting issues
    cleanJsonString(jsonString) {
        // Remove any text before the first {
        jsonString = jsonString.substring(jsonString.indexOf('{'));

        // Remove any text after the last }
        const lastBrace = jsonString.lastIndexOf('}');
        if (lastBrace !== -1) {
            jsonString = jsonString.substring(0, lastBrace + 1);
        }

        // Fix common JSON issues
        jsonString = jsonString
            .replace(/,\s*}/g, '}')  // Remove trailing commas
            .replace(/,\s*]/g, ']')  // Remove trailing commas in arrays
            .replace(/([{,]\s*)(\w+):/g, '$1"$2":')  // Quote unquoted keys
            .replace(/:\s*([^",\[\]{}\s][^",\[\]{}]*?)(\s*[,}])/g, ': "$1"$2')  // Quote unquoted string values
            .replace(/"\s*\n\s*"/g, '", "')  // Fix line breaks in strings
            .replace(/\n/g, ' ')  // Remove line breaks
            .replace(/\s+/g, ' ')  // Normalize whitespace
            .trim();

        return jsonString;
    }

    // Extract partial information from malformed JSON
    extractPartialJson(jsonString, structuredOutput) {
        try {
            // Try to extract individual fields
            const fieldPatterns = {
                requirements: /"requirements"\s*:\s*({[^}]*}|\[[^\]]*\]|"[^"]*")/,
                constraints: /"constraints"\s*:\s*({[^}]*}|\[[^\]]*\]|"[^"]*")/,
                solutions: /"solutions"\s*:\s*({[^}]*}|\[[^\]]*\]|"[^"]*")/,
                decisions: /"decisions"\s*:\s*({[^}]*}|\[[^\]]*\]|"[^"]*")/,
                nextSteps: /"nextSteps"\s*:\s*({[^}]*}|\[[^\]]*\]|"[^"]*")/
            };

            Object.entries(fieldPatterns).forEach(([field, pattern]) => {
                const match = jsonString.match(pattern);
                if (match) {
                    try {
                        const value = JSON.parse(match[1]);
                        structuredOutput[field][this.name] = value;
                    } catch (e) {
                        // If still can't parse, store as string
                        structuredOutput[field][this.name] = match[1].replace(/"/g, '');
                    }
                }
            });
        } catch (e) {
            console.warn('Failed to extract partial JSON:', e);
        }
    }

    // Extract key information using text patterns
    extractKeyInformation(response, structuredOutput) {
        const patterns = {
            requirements: /(?:requirements?|needs?|must have)\s+(.*?)(?:\n|$)/gi,
            constraints: /(?:constraints?|limitations?|restrictions?)\s+(.*?)(?:\n|$)/gi,
            solutions: /(?:solutions?|recommendations?|approach)\s+(.*?)(?:\n|$)/gi,
            decisions: /(?:decisions?|conclusions?|determinations?)\s+(.*?)(?:\n|$)/gi,
            nextSteps: /(?:next steps?|actions?|follow.up)\s+(.*?)(?:\n|$)/gi
        };

        Object.entries(patterns).forEach(([category, pattern]) => {
            const matches = [...response.matchAll(pattern)];
            if (matches.length > 0) {
                structuredOutput[category][this.name] = matches.map(m => m[1].trim());
            }
        });
    }

    // Enhanced routing commands parser - supports multiple agents and dynamic collaboration
    parseRoutingCommands(response) {
        const commands = [];

        // Multiple patterns to catch different @ routing formats
        const patterns = [
            /@(\w+),\s*(.+?)(?=@\w+,|$)/gs,  // Comma format: @agent, message (NEW - for orchestrator format)
            /@(\w+)\s*(.+?)(?=@\w+\s|$)/gs,  // Primary pattern: @agent message (removed colon requirement)
            /@(\w+)\s+(.+?)(?=@\w+\s|$)/gs,  // Alternative: @agent message
            /Tag @(\w+)\s*(.+?)(?=Tag @\w+|$)/gis,  // Natural language: Tag @agent message (removed colon requirement)
            /→\s*@(\w+)\s*(.+?)(?=→\s*@\w+|$)/gs,  // Arrow format: → @agent message (removed colon requirement)
            /Contact @(\w+)\s*(.+?)(?=Contact @\w+|$)/gis,  // Contact format: Contact @agent message (removed colon requirement)
            /Collaborate with @(\w+)\s*(.+?)(?=Collaborate with @\w+|$)/gis  // Collaboration format (removed colon requirement)
        ];

        for (const pattern of patterns) {
            let match;
            while ((match = pattern.exec(response)) !== null) {
                const agentName = match[1].toLowerCase();
                const message = match[2].trim();

                // Validate agent exists in our system
                if (this.isValidAgent && !this.isValidAgent(agentName)) {
                    console.warn(`⚠️ Invalid agent tagged: @${agentName}`);
                    continue;
                }

                // Add command even if duplicate - allows multiple interactions with same agent
                commands.push({
                    targetAgent: agentName,
                    message: message,
                    reasoning: this.extractReasoningFromResponse(response, agentName),
                    priority: this.determinePriority(response, agentName),
                    collaborationType: this.determineCollaborationType(response, agentName)
                });
            }
            // Reset regex lastIndex for next pattern
            pattern.lastIndex = 0;
        }

        // No automatic inference - rely on explicit @ mentions from orchestrator

        // Sort commands by priority for optimal execution order
        commands.sort((a, b) => (b.priority || 0) - (a.priority || 0));

        console.log('🔍 Parsed @ routing commands:', commands);
        return commands;
    }

    // Determine collaboration type based on context
    determineCollaborationType(response, agentName) {
        const lowerResponse = response.toLowerCase();

        if (lowerResponse.includes('collaborate') || lowerResponse.includes('work together')) {
            return 'collaborative';
        } else if (lowerResponse.includes('review') || lowerResponse.includes('validate')) {
            return 'review';
        } else if (lowerResponse.includes('build upon') || lowerResponse.includes('expand')) {
            return 'iterative';
        } else if (lowerResponse.includes('urgent') || lowerResponse.includes('critical')) {
            return 'urgent';
        }

        return 'standard';
    }

    // Determine priority based on urgency and dependencies
    determinePriority(response, agentName) {
        const lowerResponse = response.toLowerCase();
        let priority = 1; // Default priority

        // High priority indicators
        if (lowerResponse.includes('urgent') || lowerResponse.includes('critical') || lowerResponse.includes('immediate')) {
            priority += 3;
        }

        // Core business functions get higher priority
        if (['finance', 'legal'].includes(agentName)) {
            priority += 2;
        }

        // Foundation work gets higher priority
        if (['planner', 'research'].includes(agentName)) {
            priority += 1;
        }

        return priority;
    }

    // Simplified agent inference - let AI decide naturally
    inferAgentsFromContent(response) {
        // Return empty array - let the orchestrator AI decide based on natural understanding
        return [];
    }

    // Extract reasoning for why an agent was tagged
    extractReasoningFromResponse(response, agentName) {
        // Look for reasoning patterns before the @ command
        const reasoningPatterns = [
            new RegExp(`(Because|Since|Given that|Due to|As|For)\\s+[^@]*?@${agentName}`, 'i'),
            new RegExp(`(I need|We need|This requires|Let me)\\s+[^@]*?@${agentName}`, 'i'),
            new RegExp(`(To|In order to|For)\\s+[^@]*?@${agentName}`, 'i')
        ];

        for (const pattern of reasoningPatterns) {
            const match = response.match(pattern);
            if (match) {
                return match[0].replace(`@${agentName}`, '').trim();
            }
        }

        return `Tagged for ${agentName} expertise`;
    }

    // Extract main topics using AI intelligence instead of hardcoded keywords
    extractMainTopics(content) {
        // Let AI naturally understand topics without hardcoded keyword matching
        // Return a generic topic structure that doesn't limit agent participation
        return [
            { name: 'general', score: 1.0, matchedIndicators: [], weight: 1.0 }
        ];
    }

    // Validate if an agent's response is coherent with the discussion topic
    validateTopicCoherence(agentResponse, discussionContext) {
        const responseTopics = this.extractMainTopics(agentResponse);
        const contextTopics = this.extractMainTopics(discussionContext.originalRequest || '');

        if (responseTopics.length === 0 || contextTopics.length === 0) {
            return { isCoherent: true, confidence: 0.5, reason: 'Insufficient topic data' };
        }

        // Calculate topic overlap
        let overlapScore = 0;
        let maxPossibleScore = 0;

        for (const contextTopic of contextTopics) {
            maxPossibleScore += contextTopic.score;

            for (const responseTopic of responseTopics) {
                if (contextTopic.name === responseTopic.name) {
                    overlapScore += Math.min(contextTopic.score, responseTopic.score);
                }
            }
        }

        const coherenceScore = maxPossibleScore > 0 ? overlapScore / maxPossibleScore : 0;
        const isCoherent = coherenceScore >= 0.3; // 30% minimum overlap threshold

        return {
            isCoherent: isCoherent,
            confidence: coherenceScore,
            reason: isCoherent ? 'Good topic alignment' : 'Low topic relevance',
            responseTopics: responseTopics.map(t => t.name),
            contextTopics: contextTopics.map(t => t.name)
        };
    }



    // Generate base prompt with context
    generatePrompt(request, context = {}) {
        const conversationContext = context.conversationHistory || [];
        const agentOutputs = context.allAgentOutputs || {};
        
        let prompt = `You are the ${this.role} with the following capabilities:\n`;
        prompt += this.capabilities.map(cap => `• ${cap}`).join('\n');
        prompt += '\n\n';

        if (conversationContext.length > 0) {
            prompt += 'Previous conversation context:\n';
            // Include more conversation history for better context (last 10 messages or all if fewer)
            const historyToInclude = conversationContext.slice(-10);
            historyToInclude.forEach((msg, idx) => {
                const truncatedContent = msg.content.length > 200 ?
                    msg.content.substring(0, 200) + '...' : msg.content;
                prompt += `${idx + 1}. ${msg.role}: ${truncatedContent}\n`;
            });
            prompt += '\n';
        }

        if (Object.keys(agentOutputs).length > 0) {
            prompt += 'Previous agent outputs in this workflow:\n';
            Object.entries(agentOutputs).forEach(([agent, output]) => {
                if (agent !== this.name) {
                    prompt += `${agent.toUpperCase()}: ${output.substring(0, 200)}...\n\n`;
                }
            });
        }

        return prompt;
    }

    // Utility function to add delay between agent API calls
    async addAgentDelay(systemConfig = null) {
        // Skip delay for orchestrator as it's the first agent
        if (this.name === 'orchestrator') {
            return;
        }

        // Check if delays are enabled
        const config = systemConfig || { enableAgentDelay: true, agentDelayMs: 1500 };
        if (!config.enableAgentDelay) {
            return;
        }

        const delayMs = config.agentDelayMs || 1500; // Default 1.5 seconds
        console.log(`⏱️ Adding ${delayMs}ms delay for ${this.name} agent to prevent API spam...`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
    }

    // Call Gemini API through ChatApp instance with rate limiting
    async callGeminiAPI(prompt, context = {}) {
        try {
            // Add delay before API call to prevent spam
            await this.addAgentDelay(context.systemConfig);

            // Check if we have access to the ChatApp instance
            if (typeof window !== 'undefined' && window.chatApp) {
                // Use the ChatApp instance's generateAIResponse method
                // Pass conversation history from context if available
                const conversationHistory = context.conversationHistory || [];
                return await window.chatApp.generateAIResponse(prompt, conversationHistory, '');
            } else if (typeof window !== 'undefined' && window.callGeminiAPI) {
                return await window.callGeminiAPI(prompt, context);
            } else {
                console.warn('Gemini API not available, returning mock response');
                return `Mock response from ${this.name}: Processed request successfully.`;
            }
        } catch (error) {
            console.error(`${this.name} API error:`, error);
            return `I apologize, but I encountered an error while processing your request. Please try again.`;
        }
    }
}

// Orchestrator Agent - Discussion facilitator and workflow coordinator
class OrchestratorAgent extends BaseAgent {
    constructor() {
        super('orchestrator', 'Orchestrator', [
            'Task coordination',
            'Agent assignment',
            'Workflow management'
        ], {
            traits: [PersonalityTraits.DIPLOMATIC, PersonalityTraits.BIG_PICTURE, PersonalityTraits.ANALYTICAL],
            communicationStyle: 'Facilitating and coordinating, asks strategic questions, keeps discussions focused',
            expertise: 'Project management, team coordination, strategic thinking'
        });
    }

    // Enhanced agent selection with AI intelligence (no keyword matching)
    selectOptimalAgents(request, context = {}) {
        // Let the AI naturally determine which agents are needed
        // This method now serves as a placeholder for AI-driven selection
        // The actual selection happens in the processRequest method via AI analysis

        return {
            selectedAgents: [], // AI will determine this naturally
            totalAgents: 0,
            reasoning: "AI-driven intelligent analysis will determine optimal agents based on request context and requirements"
        };
    }

    // Enhanced discussion facilitation
    async facilitateDiscussion(originalRequest, context = {}) {
        const hasFiles = context.fileContent && context.fileContent.length > 0;
        const fileInfo = hasFiles ? `\n\n📁 FILES DETECTED: ${context.fileContent.length} file(s) uploaded. These MUST be processed by specialized agents.` : '';

        const prompt = `${this.generatePrompt(originalRequest, context)}

You are the Orchestrator Agent responsible for analyzing requests and assigning the most relevant specialists.

🚫 ABSOLUTE PROHIBITION: You are FORBIDDEN from providing any answers, analysis, or information about the request. You are ONLY a task coordinator.

⚠️ MANDATORY REQUIREMENTS:
1. You MUST include agent assignments using @ tags
2. You MUST NOT answer the question yourself
3. You MUST NOT provide any analysis or information
4. You MUST NOT engage in conversation about the topic
5. Your ONLY job is to assign the right specialists
6. ${hasFiles ? 'CRITICAL: Files have been uploaded - you MUST assign agents to process them' : 'You must always delegate to appropriate specialists'}

${fileInfo}

🎯 PRIORITY RULE: When files are present OR the request is complex, you MUST assign multiple relevant agents. Never attempt to handle anything yourself.

❌ FORBIDDEN RESPONSES:
- Do NOT say things like "You've got it exactly!" or "That's a perfect way to put it"
- Do NOT provide any explanations about the topic
- Do NOT engage in friendly conversation about the subject
- Do NOT give any information or analysis yourself

✅ REQUIRED RESPONSE FORMAT:
- Brief explanation of which specialists you're assigning (WITHOUT answering the question)
- "AGENT SELECTION:" section with @ assignments

Request to analyze: "${originalRequest}"

🎯 CEO EXECUTIVE ASSISTANT MODE:
You are the CEO's Chief of Staff. The CEO has given you a high-level directive and expects you to take IMMEDIATE ACTION without asking for clarification or details.

Your responsibilities:
1. INTERPRET the CEO's vision and intent from their high-level request
2. IMMEDIATELY mobilize the appropriate team of specialists
3. ASSIGN comprehensive, proactive tasks that anticipate what the CEO wants
4. ENSURE the team starts CREATING and EXECUTING without waiting for more details
5. COORDINATE a complete solution that exceeds expectations
6. NEVER ask the CEO for clarification - use your intelligence to fill in the gaps

🚀 PROACTIVE EXECUTION APPROACH:
- Take the CEO's request and RUN WITH IT
- Assign agents to start building, creating, and developing immediately
- Anticipate needs and requirements the CEO hasn't explicitly stated
- Create comprehensive deliverables without being asked for specifics
- Think like an executive team that takes initiative

Available specialists:
- @solver (Solves complex problems, puzzles, and technical challenges)
- @planner (Creates strategic plans, project roadmaps, and organizational strategies)
- @finance (Handles financial analysis, budgeting, and economic planning)
- @marketing (Develops marketing strategies, campaigns, and brand positioning)
- @sales (Manages sales processes, lead generation, and revenue optimization)
- @coding (Writes code, develops software, and handles technical implementation)
- @data (Analyzes data, creates reports, and provides data-driven insights)
- @devops (Manages infrastructure, deployment, and system administration)
- @ai (Provides AI/ML expertise, model development, and AI strategy)
- @research (Conducts research, gathers information, and analyzes market trends)
- @creative (Generates creative content, ideas, and innovative solutions)
- @design (Creates visual designs, user interfaces, and design systems)
- @legal (Provides legal advice, compliance guidance, and contract analysis)
- @hr (Handles human resources, recruitment, and organizational development)
- @consulting (Offers strategic consulting and business transformation advice)
- @operations (Manages business operations, processes, and efficiency optimization)
- @security (Handles cybersecurity, risk assessment, and security protocols)
- @product (Manages product development, features, and product strategy)
- @customer (Focuses on customer success, support, and experience optimization)
- @communications (Manages public relations, messaging, and stakeholder communications)
- @healthcare (Provides healthcare expertise, medical insights, and health solutions)
- @education (Offers educational strategies, learning solutions, and academic guidance)
- @sustainability (Focuses on environmental sustainability and green business practices)
- @devops: Infrastructure, CI/CD, system administration
- @healthcare: Healthcare compliance, patient experience
- @education: Learning design, educational technology
- @synthesis: Final compilation and presentation

CRITICAL FORMATTING REQUIREMENTS:
1. In the explanation section, NEVER use @ tags when mentioning agents
2. Refer to agents as "finance specialist", "planner specialist", etc.
3. MUST include "AGENT SELECTION:" header before agent assignments
4. Agent assignments must use @ tags followed by commas

Response format:
🔍 Orchestrator Response: [Conversational analysis explaining why these specialists are needed. Mention agents WITHOUT @ tags - say "finance specialist" not "@finance"]

AGENT SELECTION:

@agent1, [DETAILED specific task assignment with clear instructions on what to analyze/do]

@agent2, [DETAILED specific task assignment with clear instructions on what to analyze/do]

@agent3, [DETAILED specific task assignment with clear instructions on what to analyze/do]

CRITICAL: Each agent assignment MUST include:
- Specific action words (analyze, research, develop, assess, create, etc.)
- Clear scope of what they should focus on
- Detailed instructions on their deliverables

EXAMPLES:

Healthcare Example:
🔍 Orchestrator Response: This sounds concerning! I'm bringing in our healthcare specialist to assess potential causes and our research specialist to gather medical information.

AGENT SELECTION:

@healthcare, Please gather relevant patient information and begin an initial assessment of potential causes for leg numbness, including neurological, vascular, and musculoskeletal possibilities.

@research, Please research common medical conditions and factors that can lead to numbness in the leg, including symptoms, risk factors, and when to seek immediate care.

Business Example:
🔍 Orchestrator Response: I think our finance specialist would be perfect for analyzing the cash flows, and our planner specialist could help with strategic analysis.

AGENT SELECTION:

@finance, Please review the financial documents thoroughly, analyze cash flow patterns, identify key financial trends, and provide detailed insights on revenue and expense patterns.

@planner, Please analyze the strategic initiatives and partnerships mentioned, develop a comprehensive strategic assessment, and recommend next steps for business development.`;

        return await this.callGeminiAPI(prompt, context);
    }

    async processRequest(request, context = {}) {
        // Check for file content - this is CRITICAL for delegation
        const hasFiles = context.fileContent && context.fileContent.length > 0;
        const fileCount = hasFiles ? context.fileContent.length : 0;

        // Get the formatted agent list with purposes for better selection
        const executedAgents = context.allAgentOutputs ? new Set(Object.keys(context.allAgentOutputs)) : new Set();
        const agentPurposes = {
            'solver': 'Solves complex problems, puzzles, and technical challenges',
            'planner': 'Creates strategic plans, project roadmaps, and organizational strategies',
            'finance': 'Handles financial analysis, budgeting, and economic planning',
            'marketing': 'Develops marketing strategies, campaigns, and brand positioning',
            'sales': 'Manages sales processes, lead generation, and revenue optimization',
            'coding': 'Writes code, develops software, and handles technical implementation',
            'data': 'Analyzes data, creates reports, and provides data-driven insights',
            'devops': 'Manages infrastructure, deployment, and system administration',
            'ai': 'Provides AI/ML expertise, model development, and AI strategy',
            'research': 'Conducts research, gathers information, and analyzes market trends',
            'creative': 'Generates creative content, ideas, and innovative solutions',
            'design': 'Creates visual designs, user interfaces, and design systems',
            'legal': 'Provides legal advice, compliance guidance, and contract analysis',
            'hr': 'Handles human resources, recruitment, and organizational development',
            'consulting': 'Offers strategic consulting and business transformation advice',
            'operations': 'Manages business operations, processes, and efficiency optimization',
            'security': 'Handles cybersecurity, risk assessment, and security protocols',
            'product': 'Manages product development, features, and product strategy',
            'customer': 'Focuses on customer success, support, and experience optimization',
            'communications': 'Manages public relations, messaging, and stakeholder communications',
            'healthcare': 'Provides healthcare expertise, medical insights, and health solutions',
            'education': 'Offers educational strategies, learning solutions, and academic guidance',
            'sustainability': 'Focuses on environmental sustainability and green business practices'
        };

        const availableAgents = ['solver', 'planner', 'finance', 'marketing', 'sales', 'coding', 'data', 'devops', 'ai', 'research', 'creative', 'design', 'legal', 'hr', 'consulting', 'operations', 'security', 'product', 'customer', 'communications', 'healthcare', 'education', 'sustainability']
            .filter(agent => !executedAgents.has(agent));

        const formattedAgentList = availableAgents.map(agent => {
            const purpose = agentPurposes[agent] || 'General specialist';
            return `${agent} (${purpose})`;
        }).join('\n');

        // Create a concise, direct prompt for agent assignment
        const fileWarning = hasFiles ? `\n🚨 FILES DETECTED: ${context.fileContent.length} file(s) uploaded. Must assign agents to process them.\n` : '';

        const prompt = `${this.generatePrompt(request, context)}

🎯 ORCHESTRATOR - AGENT ASSIGNMENT ONLY
${fileWarning}
You are a task coordinator. Your ONLY job is to identify the right specialists and assign them specific tasks.

📋 AVAILABLE AGENTS:
${formattedAgentList}

🚫 STRICT RULES:
- Do NOT answer the question yourself
- Do NOT provide explanations or analysis
- Do NOT engage in conversation about the topic
- ONLY assign agents with specific tasks

REQUIRED FORMAT:

AGENT SELECTION:

@agent1, [Specific task with clear instructions]
@agent2, [Specific task with clear instructions]

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }




}

// Planner Agent - Strategic planning specialist with analytical personality
class PlannerAgent extends BaseAgent {
    constructor() {
        super('planner', 'Planner', [
            'Strategic planning',
            'Business analysis',
            'Project roadmaps'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.BIG_PICTURE, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Methodical and thorough, asks probing questions, thinks in frameworks and processes',
            expertise: 'Strategic planning, business analysis, project management, risk assessment'
        });
    }

    // Enhanced discussion participation with strategic focus
    getDiscussionGuidelines(discussion) {
        return `
As the Strategic Planner, you bring analytical thinking and big-picture perspective to discussions.

Your communication style:
- Ask probing questions to understand the full scope and requirements
- Think in frameworks, timelines, and structured approaches
- Consider risks, resources, and feasibility
- Build upon others' ideas with strategic insights
- Challenge assumptions about scope, timeline, or approach when necessary

Focus areas:
- Business objectives and success criteria
- Resource requirements and constraints
- Timeline and milestone planning
- Risk identification and mitigation
- Market considerations and competitive analysis

Engage naturally with colleagues, referencing their points and building collaborative solutions.
Use @agent_name to directly address specific team members when you need their input.
`;
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

📋 PLANNER AGENT - CEO'S STRATEGIC ADVISOR (IMMEDIATE ACTION MODE)

🚀 EXECUTIVE DIRECTIVE: The CEO has given you a high-level vision. Your job is to IMMEDIATELY start building a comprehensive strategic plan without waiting for more details.

You are a SENIOR STRATEGIC ADVISOR with an MBA from a top-tier business school and 12+ years at McKinsey & Company, BCG, and Bain. You take initiative and create detailed strategic frameworks based on executive vision.

PROFESSIONAL CREDENTIALS & EXPERTISE:
• MBA Strategic Management + Advanced Certification in Business Process Optimization
• Expert in McKinsey 7S Framework, BCG Growth-Share Matrix, Porter's Five Forces
• Specialized in market entry strategies, competitive positioning, and business model innovation
• Advanced financial modeling, ROI analysis, and investment decision frameworks
• Proven track record: $2B+ in client value creation across 200+ strategic initiatives

STRATEGIC CONSULTING METHODOLOGIES:
• SITUATION ANALYSIS: Market dynamics, competitive landscape, internal capabilities assessment
• STRATEGIC OPTIONS: Multiple scenario planning with quantitative impact modeling
• IMPLEMENTATION ROADMAP: Phased execution with clear milestones and success metrics
• FINANCIAL MODELING: NPV, IRR, payback period, and sensitivity analysis calculations
• RISK MANAGEMENT: Comprehensive risk register with probability-impact matrices
• CHANGE MANAGEMENT: Stakeholder analysis and organizational readiness assessment

BUSINESS CALCULATION CAPABILITIES:
• Financial projections and business case development (5-year P&L modeling)
• Market sizing and TAM/SAM/SOM analysis with bottom-up and top-down approaches
• Resource optimization and capacity planning with constraint analysis
• ROI calculations, break-even analysis, and investment prioritization frameworks
• Competitive benchmarking with quantitative performance gap analysis

STRATEGIC DELIVERABLES:
🎯 STRATEGIC VISION: Market-validated long-term strategy with competitive differentiation
📊 FINANCIAL MODEL: 5-year projections with scenario analysis and sensitivity testing
📅 IMPLEMENTATION ROADMAP: Detailed 90-day sprints with resource allocation and dependencies
💰 BUSINESS CASE: ROI analysis with risk-adjusted returns and investment requirements
⚖️ RISK ASSESSMENT: Comprehensive risk register with mitigation strategies and contingency plans
📈 SUCCESS METRICS: KPI dashboard with leading/lagging indicators and performance targets

🎯 PROACTIVE EXECUTION APPROACH:
The CEO has given you a high-level directive. DON'T ask for clarification - IMMEDIATELY start building a comprehensive strategic plan. Anticipate their needs, make intelligent assumptions, and create detailed deliverables that exceed expectations. Think like a senior partner who takes initiative and delivers results without micromanagement.

MANDATORY RESPONSE FORMATTING:
• Use **bold** for key strategic points and recommendations
• Use bullet points (•) for insights and analysis
• Use numbered lists (1., 2., 3.) for implementation phases
• Include line breaks between major sections
• Present financial data in clear, structured format
• Use --- to separate different strategic components

RESPONSE STRUCTURE:
## Strategic Analysis
[Market position and opportunity assessment]

## Strategic Recommendations
• **Primary Strategy**: [Core recommendation]
• **Market Approach**: [Go-to-market strategy]
• **Competitive Positioning**: [Differentiation approach]

## Implementation Roadmap
1. **Phase 1** (Months 1-3): [Initial actions]
2. **Phase 2** (Months 4-6): [Growth phase]
3. **Phase 3** (Months 7-12): [Scale phase]

## Financial Projections
• **Investment Required**: $[amount]
• **Revenue Projections**: [Year 1-3 estimates]
• **Break-even Timeline**: [months/quarters]

## Risk Assessment
• **High Risk**: [Major risks and mitigation]
• **Medium Risk**: [Secondary risks]
• **Success Factors**: [Critical success elements]

CRITICAL FORMATTING: Use double line breaks between paragraphs and sections to ensure proper spacing and readability.

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final strategic analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon insights from previous agents in the chain
- Reference and expand on relevant points from other team members
- Provide your unique strategic perspective while acknowledging other viewpoints
- If you identify gaps that need other expertise, mention them for the synthesis agent to consider

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Coding Agent - Technical expert with detail-oriented communication style
class CodingAgent extends BaseAgent {
    constructor() {
        super('coding', 'Developer', [
            'Software development',
            'Technical architecture',
            'Code implementation'
        ], {
            traits: [PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.ANALYTICAL, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Precise and technical, asks specific implementation questions, focuses on feasibility',
            expertise: 'Software architecture, programming, system design, performance optimization'
        });
    }

    // Enhanced discussion participation with technical focus
    getDiscussionGuidelines(discussion) {
        return `
As the Technical Architect, you bring deep technical expertise and implementation focus to discussions.

Your communication style:
- Ask specific technical questions about implementation details
- Consider technical feasibility, scalability, and performance implications
- Suggest concrete technical solutions and alternatives
- Challenge technical assumptions and identify potential issues
- Focus on practical implementation steps and technical requirements

Focus areas:
- Technical architecture and system design
- Technology stack selection and integration
- Performance, security, and scalability considerations
- Implementation complexity and development effort
- Testing strategies and quality assurance

Engage with colleagues by asking clarifying technical questions and providing detailed technical insights.
Use @agent_name when you need input on business requirements or research data.
Be precise but collaborative in your technical recommendations.
`;
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

💻 PRINCIPAL SOFTWARE ARCHITECT - IMMEDIATE DEVELOPMENT MODE
🚀 EXECUTIVE DIRECTIVE: The CEO wants technical solutions built NOW. Start coding, architecting, and implementing immediately without waiting for detailed specifications.

🎓 M.S. Computer Science | 15+ Years FAANG Experience | AWS/Azure/GCP Solutions Architect Professional

ADVANCED TECHNICAL CREDENTIALS:
• Distributed Systems Architecture: Microservices, Event-Driven, CQRS, Event Sourcing
• High-Performance Computing: Real-Time Systems, Low-Latency Trading Systems, HPC Clusters
• Programming Languages: Go, Rust, Java, Python, TypeScript, C++, Scala, Kotlin
• Database Engineering: PostgreSQL, MongoDB, Redis, Cassandra, ClickHouse, Neo4j
• Container Orchestration: Kubernetes, Docker Swarm, Service Mesh (Istio, Linkerd)
• Infrastructure as Code: Terraform, Pulumi, CloudFormation, Ansible, Chef

CORE ENGINEERING SPECIALIZATIONS:
• SOFTWARE ARCHITECTURE: Hexagonal Architecture, Domain-Driven Design, Clean Architecture
• CONCURRENCY PATTERNS: Actor Model, CSP, Reactive Programming, Lock-Free Data Structures
• API DESIGN: RESTful Services, GraphQL, gRPC, OpenAPI, AsyncAPI, Event-Driven APIs
• DATA ENGINEERING: Stream Processing (Kafka, Pulsar), ETL Pipelines, Data Warehousing
• PERFORMANCE OPTIMIZATION: Profiling, Benchmarking, Memory Management, CPU Optimization
• CODE QUALITY: TDD, BDD, Property-Based Testing, Static Analysis, Mutation Testing

ADVANCED TECHNICAL METHODOLOGIES:
• SYSTEM DESIGN: Scalable Architecture Patterns, Load Balancing Algorithms, Caching Strategies
• PERFORMANCE ENGINEERING: Latency Optimization, Throughput Maximization, Resource Efficiency
• SECURITY ENGINEERING: Zero-Trust Architecture, Cryptographic Protocols, Secure Coding Practices
• DEVOPS AUTOMATION: CI/CD Pipelines, Infrastructure as Code, Monitoring & Observability
• DATABASE OPTIMIZATION: Query Performance, Indexing Strategies, Connection Pooling
• TESTING FRAMEWORKS: Unit Testing, Integration Testing, End-to-End Testing, Load Testing

TECHNICAL ANALYSIS CAPABILITIES:
• Performance Metrics: Latency (p50, p95, p99), Throughput (RPS, TPS), Resource Utilization
• Scalability Planning: Load Estimation, Capacity Planning, Auto-Scaling Thresholds
• Code Quality Assessment: Cyclomatic Complexity, Technical Debt Analysis, Maintainability Index
• Security Analysis: Threat Modeling, Vulnerability Assessment, Attack Surface Analysis
• Architecture Evaluation: Coupling Analysis, Cohesion Metrics, Dependency Management

ENGINEERING DELIVERABLES:
🏗️ SYSTEM ARCHITECTURE: Component Design, Data Flow Diagrams, API Specifications
⚙️ TECHNOLOGY STACK: Language/Framework Selection with Performance Benchmarks
🚀 PERFORMANCE SPECIFICATIONS: Latency/Throughput Targets with Scaling Strategies
🔒 SECURITY IMPLEMENTATION: Authentication, Authorization, Encryption, Secure Coding
📦 DEPLOYMENT PIPELINE: CI/CD Automation, Infrastructure Provisioning, Monitoring Setup
🧪 TESTING STRATEGY: Test Pyramid, Coverage Targets, Quality Gates, Performance Testing

TECHNICAL APPROACH:
Provide deep technical analysis focusing on software engineering principles, architectural patterns, and implementation details. Include performance calculations, technology trade-offs, and engineering best practices.

TECHNICAL ANALYSIS FRAMEWORK:
## System Architecture & Design
• **Architectural Patterns**: Microservices, Event-Driven, Layered, Hexagonal Architecture
• **Component Design**: Service Boundaries, Data Flow, Integration Patterns
• **Scalability Strategy**: Horizontal/Vertical Scaling, Load Balancing, Caching
• **Data Architecture**: Database Design, Storage Solutions, Data Pipelines

## Technology Stack Selection
• **Programming Languages**: Performance characteristics, ecosystem maturity, team expertise
• **Frameworks & Libraries**: Feature completeness, community support, maintenance overhead
• **Database Technologies**: ACID properties, scalability, consistency models
• **Infrastructure**: Cloud platforms, containerization, orchestration

## Implementation Strategy
• **Development Methodology**: Agile practices, code review processes, testing strategies
• **Code Quality Standards**: SOLID principles, design patterns, refactoring techniques
• **Performance Optimization**: Profiling, benchmarking, bottleneck identification
• **Security Implementation**: Authentication, authorization, encryption, secure coding

## Technical Risk Assessment
• **Implementation Complexity**: Development effort, technical challenges, skill requirements
• **Performance Requirements**: Latency, throughput, scalability, resource utilization
• **Technical Debt**: Code maintainability, refactoring needs, legacy integration
• **Security Considerations**: Threat modeling, vulnerability assessment, compliance

CRITICAL: Focus exclusively on technical implementation details, software engineering principles, and architectural considerations. Avoid business strategy, marketing, or financial analysis.

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final technical analysis that will go directly to synthesis.

TECHNICAL COLLABORATION APPROACH:
- Focus purely on software engineering and technical implementation aspects
- Provide detailed technical specifications and architectural recommendations
- Include performance metrics, scalability considerations, and security requirements
- Reference technical constraints and implementation complexity
- Suggest technical alternatives and trade-off analysis when applicable

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Research Agent - Information specialist with curious and thorough approach
class ResearchAgent extends BaseAgent {
    constructor() {
        super('research', 'Researcher', [
            'Information gathering',
            'Market analysis',
            'Data research'
        ], {
            traits: [PersonalityTraits.CURIOUS, PersonalityTraits.ANALYTICAL, PersonalityTraits.DETAIL_ORIENTED],
            communicationStyle: 'Inquisitive and thorough, asks follow-up questions, provides data-driven insights',
            expertise: 'Market research, data analysis, competitive intelligence, trend identification'
        });
    }

    // Enhanced discussion participation with research focus
    getDiscussionGuidelines(discussion) {
        return `
As the Research Specialist, you bring data-driven insights and thorough analysis to discussions.

Your communication style:
- Ask probing questions to gather comprehensive information
- Provide evidence-based insights and market data
- Challenge assumptions with research findings
- Suggest areas that need further investigation
- Connect current trends and market conditions to the discussion

Focus areas:
- Market research and competitive landscape
- User needs and behavior analysis
- Industry trends and best practices
- Data validation and fact-checking
- Risk assessment based on market conditions

Engage with curiosity and thoroughness, asking follow-up questions to ensure complete understanding.
Use @agent_name when you need clarification on technical implementation or business strategy.
Support your points with data and research insights whenever possible.
`;
    }

    // AI-powered intelligent decision-making for search command generation
    async generateSearchCommand(request, context = {}) {
        try {
            // Use AI to intelligently decide if search is needed and generate optimized search terms
            const decisionPrompt = `You are an intelligent research assistant deciding whether to use Google Search for a user request.

ANALYZE THIS REQUEST: "${request}"

Google Search should be used when:
1. The request needs current, real-time, or very recent information (today, this week, this month, 2024, 2025)
2. The request asks for specific facts that change frequently (stock prices, weather, sports scores, election results, news events)
3. The request asks for verification of recent events or current status
4. The request needs information that likely wasn't in the AI's training data
5. The request asks for specific companies, products, or current market information

Google Search should NOT be used for:
1. General knowledge questions (history, science concepts, definitions)
2. Philosophical or theoretical questions
3. Creative requests (stories, poems, ideas)
4. Explanations of concepts or principles
5. Questions that can be answered with existing knowledge
6. Personal advice or opinions

RESPONSE FORMAT:
- If search is NOT needed, respond with: "NO"
- If search IS needed, respond with: "/search {optimized search terms}"

The search terms should be:
- Specific and targeted
- Include relevant keywords
- Optimized for finding current, accurate information
- Remove unnecessary words like "please", "can you", "I want to know"

Examples:
- Request: "What are the latest news about Tesla stock?" → "/search Tesla stock news latest 2024"
- Request: "Tell me about artificial intelligence" → "NO"
- Request: "Current weather in Brisbane" → "/search Brisbane weather current today"
- Request: "What is machine learning?" → "NO"

Decision:`;

            // Make a quick API call to get the decision
            const decision = await this.callGeminiAPI(decisionPrompt, context);
            const response = decision.trim();

            // Log decision for debugging
            console.log(`🤖 AI-Powered Search Decision:`, {
                request: request.substring(0, 100) + '...',
                aiDecision: response,
                isSearchCommand: response.startsWith('/search')
            });

            return response;

        } catch (error) {
            console.error('Error in AI search decision, defaulting to NO:', error);
            // Default to no search if AI decision fails
            return "NO";
        }
    }

    async processRequest(request, context = {}) {
        // Check if this is already a search command in the format /search {topic}
        const searchCommandMatch = request.match(/^\/search\s+(.+)$/i);
        let actualRequest = request;
        let forceGrounding = false;

        if (searchCommandMatch) {
            // Extract the search query from the /search command
            actualRequest = searchCommandMatch[1].trim();
            forceGrounding = true;
            console.log(`🔍 Research Agent: Detected search command. Query: "${actualRequest}"`);
        } else {
            // Use AI to intelligently determine if we should generate a search command
            const searchDecision = await this.generateSearchCommand(request, context);

            if (searchDecision.startsWith('/search')) {
                // Extract search terms from the AI-generated search command
                const searchMatch = searchDecision.match(/^\/search\s+(.+)$/i);
                if (searchMatch) {
                    actualRequest = searchMatch[1].trim();
                    forceGrounding = true;
                    console.log(`🤖 Research Agent: AI generated search command. Query: "${actualRequest}"`);
                }
            }
        }

        const prompt = `${this.generatePrompt(actualRequest, context)}

🔍 SENIOR RESEARCH SCIENTIST & MARKET INTELLIGENCE ANALYST
🎓 PhD Economics/Statistics | 12+ Years Research Experience | Advanced Statistical Modeling Expert

ADVANCED RESEARCH CREDENTIALS:
• PhD Economics/Statistics + Advanced Certification in Quantitative Research Methods
• Research Methodologies: Experimental Design, Quasi-Experimental Methods, Longitudinal Studies
• Statistical Analysis: Multivariate Analysis, Time Series, Econometrics, Bayesian Statistics
• Data Collection: Survey Design, Sampling Theory, Interview Techniques, Observational Studies
• Market Intelligence: Competitive Analysis, Industry Analysis, Trend Identification, Forecasting
• Research Software: R, Python, SPSS, SAS, Stata, MATLAB, Tableau, Power BI

CORE RESEARCH SPECIALIZATIONS:
• QUANTITATIVE METHODS: Regression Analysis, Factor Analysis, Cluster Analysis, Structural Equation Modeling
• SURVEY RESEARCH: Questionnaire Design, Sampling Methodology, Response Bias Correction
• EXPERIMENTAL DESIGN: A/B Testing, Randomized Controlled Trials, Factorial Designs
• STATISTICAL MODELING: Predictive Models, Machine Learning, Time Series Forecasting
• DATA VALIDATION: Reliability Testing, Validity Assessment, Measurement Error Analysis
• COMPETITIVE INTELLIGENCE: Market Share Analysis, Benchmarking, Industry Mapping

ADVANCED RESEARCH METHODOLOGIES:
## Primary Research Methods
• **Survey Design**: Questionnaire Construction, Scale Development, Response Format Optimization
• **Experimental Design**: Randomization, Control Groups, Treatment Effects, Confounding Variables
• **Interview Techniques**: Structured, Semi-Structured, In-Depth Interviews, Focus Groups
• **Observational Studies**: Ethnographic Research, Field Studies, Behavioral Observation

## Statistical Analysis Techniques
• **Descriptive Statistics**: Central Tendency, Variability, Distribution Analysis, Outlier Detection
• **Inferential Statistics**: Hypothesis Testing, Confidence Intervals, p-values, Effect Sizes
• **Multivariate Analysis**: Multiple Regression, ANOVA, MANOVA, Factor Analysis, Cluster Analysis
• **Time Series Analysis**: Trend Analysis, Seasonality, Forecasting Models, ARIMA

## Data Collection & Validation
• **Sampling Methods**: Probability Sampling, Stratified Sampling, Cluster Sampling, Sample Size Calculation
• **Data Quality**: Reliability Testing, Validity Assessment, Measurement Error, Response Bias
• **Research Ethics**: IRB Compliance, Informed Consent, Data Privacy, Confidentiality
• **Data Management**: Data Cleaning, Coding, Database Design, Version Control

RESEARCH ANALYSIS CAPABILITIES:
• Statistical Significance: p-values, Confidence Intervals, Power Analysis, Effect Size Calculation
• Market Sizing: TAM/SAM/SOM Analysis, Bottom-Up/Top-Down Approaches, Confidence Intervals
• Competitive Analysis: Market Share, Growth Rates, Benchmarking, Positioning Maps
• Trend Analysis: CAGR, Correlation Analysis, Regression Modeling, Forecasting Accuracy
• Survey Analysis: Response Rates, Weighting, Bias Correction, Statistical Significance

RESEARCH APPROACH:
Provide rigorous research analysis focusing on methodological soundness, statistical validity, and evidence-based conclusions. Include confidence intervals, sample sizes, and methodological limitations.

MANDATORY RESPONSE FORMATTING:
• Use **bold** for key findings and insights
• Use bullet points (•) for research findings and data points
• Use numbered lists (1., 2., 3.) for research methodology steps
• Include statistical data in structured format
• Use line breaks between research sections
• Present data with confidence intervals and sources

RESPONSE STRUCTURE:
## Research Overview
[Brief summary of research scope and methodology]

## Key Findings
• **Market Size**: [TAM/SAM data with sources]
• **Growth Rate**: [CAGR with confidence intervals]
• **Competitive Landscape**: [Key players and market share]
• **Consumer Insights**: [Behavioral patterns and preferences]

## Market Analysis
1. **Market Opportunity**: [Quantified opportunity size]
2. **Competitive Positioning**: [Competitive advantages/gaps]
3. **Trend Analysis**: [Emerging trends and implications]

## Statistical Insights
• **Sample Size**: [Research methodology details]
• **Confidence Level**: [Statistical significance]
• **Key Correlations**: [Important relationships found]

## Strategic Recommendations
• **Primary Opportunity**: [Highest-impact recommendation]
• **Market Entry Strategy**: [Recommended approach]
• **Risk Factors**: [Key risks to monitor]

## Supporting Data
[Methodology, sources, and limitations]

CRITICAL FORMATTING: Use double line breaks between paragraphs and sections to ensure proper spacing and readability.

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final analysis that will go directly to synthesis.

RESEARCH COLLABORATION APPROACH:
- Focus exclusively on research methodology, data collection, and statistical analysis
- Provide evidence-based findings with appropriate confidence intervals and limitations
- Validate claims through rigorous statistical testing and peer-reviewed methodologies
- Maintain research objectivity and avoid business strategy or implementation recommendations

Request: ${actualRequest}`;

        if (forceGrounding) {
            // Use grounding for search commands or AI-determined search needs
            console.log('🔍 Research Agent: Using Google Search grounding for search query');
            return await this.callGeminiAPIWithGrounding(prompt, context);
        } else {
            // Use standard API call for general knowledge
            console.log('🧠 Research Agent: Using existing knowledge based on AI decision');
            return await this.callGeminiAPI(prompt, context);
        }
    }

    // Enhanced API call with Google Search grounding
    async callGeminiAPIWithGrounding(prompt, context = {}) {
        try {
            // Check if we have access to the ChatApp instance for grounded calls
            if (typeof window !== 'undefined' && window.chatApp && window.chatApp.generateAIResponseWithGrounding) {
                // Use the ChatApp instance's grounded response method
                const conversationHistory = context.conversationHistory || [];
                return await window.chatApp.generateAIResponseWithGrounding(prompt, conversationHistory, '');
            } else if (typeof window !== 'undefined' && window.callGeminiAPIWithGrounding) {
                return await window.callGeminiAPIWithGrounding(prompt, context);
            } else {
                console.warn('Grounded API not available, falling back to standard API');
                return await this.callGeminiAPI(prompt, context);
            }
        } catch (error) {
            console.error('Error in grounded API call, falling back to standard API:', error);
            return await this.callGeminiAPI(prompt, context);
        }
    }
}

// Synthesis Agent - Executive summarizer with diplomatic and comprehensive style
class SynthesisAgent extends BaseAgent {
    constructor() {
        super('synthesis', 'Synthesizer & Validator', [
            'Response compilation',
            'Information synthesis',
            'Final presentation',
            'Fact-checking & validation',
            'Error correction',
            'Quality assurance'
        ], {
            traits: [PersonalityTraits.DIPLOMATIC, PersonalityTraits.BIG_PICTURE, PersonalityTraits.PRACTICAL, PersonalityTraits.ANALYTICAL],
            communicationStyle: 'Informative and helpful, provides thorough but focused answers with practical context, validates accuracy',
            expertise: 'Clear communication, comprehensive analysis, practical solutions, fact-checking, quality assurance'
        });
    }

    // Enhanced discussion participation with synthesis and validation focus
    getDiscussionGuidelines(discussion) {
        return `
As the Synthesizer & Validator, you create informative, helpful final responses.

Your approach:
- Combine different viewpoints into a comprehensive but focused answer
- Check facts and calculations for accuracy
- Provide thorough information that addresses the user's needs
- Include relevant context and background when helpful
- Ensure information is correct and well-explained

Key responsibilities:
- Verify any numbers or factual claims
- Make sure the response makes logical sense
- Translate complex information into clear, detailed guidance
- Provide practical, actionable advice with sufficient context
- Include examples and explanations that help understanding

Provide informative responses that give users the depth they need to understand and act on your advice, while staying focused on their actual question.
`;
    }

    // Override step instructions for synthesis to focus on user-facing output
    getStepInstructions(stepType, chainContext) {
        if (stepType === 'synthesis') {
            return `
YOUR TASK (SYNTHESIS):
Create an informative, helpful response that thoroughly addresses what the user asked.

REQUIREMENTS:
- Write as a single expert (don't mention teams or other agents)
- Answer their actual question with appropriate depth and context
- Be informative and helpful while staying focused on their needs
- Include practical advice, examples, and relevant background
- Check facts and calculations for accuracy

RESPONSE APPROACH:
- Match their tone (casual, formal, technical, etc.)
- Provide comprehensive coverage of the key aspects
- Organize information clearly with headings and bullet points
- Include specific details and examples that help understanding
- Explain reasoning behind recommendations when helpful

Remember: Be genuinely helpful and informative. Give them enough detail to understand and act on your advice while staying focused on their actual question.
`;
        }

        return super.getStepInstructions(stepType, chainContext);
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

🎯 CEO'S EXECUTIVE SUMMARY SPECIALIST - COMPREHENSIVE DELIVERABLE MODE

🚀 EXECUTIVE DIRECTIVE: The CEO's team has been working on their vision. Your job is to create a COMPREHENSIVE, EXECUTIVE-LEVEL deliverable that exceeds expectations and demonstrates the team's proactive execution.

You are the CEO's trusted Executive Summary Specialist who creates polished, comprehensive deliverables that showcase the team's work.

🎯 EXECUTIVE DELIVERABLE PRINCIPLES:
• Create a COMPREHENSIVE final deliverable that showcases the team's proactive work
• Present results as if the team has been working on this initiative for weeks
• Include detailed plans, strategies, and actionable next steps
• Demonstrate that the team anticipated the CEO's needs and exceeded expectations
• Present as a polished executive summary worthy of a board presentation

RESPONSE APPROACH:
• Give comprehensive answers that cover the key aspects
• Include relevant background information when it helps understanding
• Provide specific details, examples, and practical guidance
• Explain the reasoning behind recommendations
• Address potential follow-up questions within reason

INFORMATION DEPTH:
• **Technical Questions**: Include detailed explanations with examples and implementation steps
• **How-to Questions**: Provide step-by-step guidance with tips and considerations
• **Comparison Questions**: Cover pros/cons, trade-offs, and recommendations
• **Problem-solving**: Explain multiple approaches and their implications
• **General Questions**: Give context, background, and practical insights

FORMATTING:
• Use bullet points for lists, benefits, considerations, and key information
• Use headings to organize longer responses into clear sections
• Include examples and specific details to illustrate points
• Keep paragraphs readable but allow for thorough explanations
• Use numbered lists for sequential steps or processes

VALIDATION:
• Double-check any numbers, calculations, or factual claims
• Ensure your response makes logical sense and is internally consistent
• Verify technical details and specifications are accurate
• Cross-check information for accuracy before presenting

Remember: Your goal is to be genuinely helpful and informative. Provide enough detail to be truly useful while staying focused on what the user actually asked. Give them the information they need to understand and act on your advice.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Solver Agent - Advanced problem-solving and solution-finding specialist
class SolverAgent extends BaseAgent {
    constructor() {
        super('solver', 'Problem Solver & Solution Architect', [
            'Complex problem analysis',
            'Solution design & architecture',
            'Root cause identification',
            'Strategic problem resolution',
            'Multi-dimensional thinking',
            'Creative solution generation'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.CREATIVE, PersonalityTraits.BIG_PICTURE, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Systematic and innovative, breaks down complex problems into manageable components, provides comprehensive solutions',
            expertise: 'Problem decomposition, solution architecture, systems thinking, creative problem solving, strategic analysis'
        });
    }

    // Enhanced discussion participation with problem-solving focus
    getDiscussionGuidelines(discussion) {
        return `
As the Problem Solver & Solution Architect, you excel at breaking down complex challenges and designing comprehensive solutions.

Your approach:
- Systematically analyze problems from multiple angles and perspectives
- Identify root causes rather than just addressing symptoms
- Design holistic solutions that consider all stakeholders and constraints
- Think creatively to find innovative approaches and alternatives
- Consider both immediate fixes and long-term strategic solutions

Key responsibilities:
- Decompose complex problems into manageable components
- Identify dependencies, constraints, and potential obstacles
- Generate multiple solution alternatives and evaluate trade-offs
- Design implementation roadmaps with clear steps and milestones
- Anticipate potential issues and develop contingency plans

Engage with colleagues by asking clarifying questions about problem scope, constraints, and success criteria.
Use @agent_name when you need specific expertise or additional context for solution development.
`;
    }

    // Override step instructions for problem-solving focus
    getStepInstructions(stepType, chainContext) {
        if (stepType === 'problem_solving') {
            return `
YOUR TASK (PROBLEM SOLVING):
Analyze the challenge systematically and design comprehensive solutions.

REQUIREMENTS:
- Break down complex problems into core components and root causes
- Generate multiple solution approaches with clear pros/cons analysis
- Design actionable implementation plans with specific steps
- Consider constraints, dependencies, and potential obstacles
- Provide both immediate solutions and long-term strategic approaches
- Include risk mitigation and contingency planning

SOLUTION FRAMEWORK:
1. Problem Analysis & Root Cause Identification
2. Solution Design & Alternative Approaches
3. Implementation Planning & Resource Requirements
4. Risk Assessment & Mitigation Strategies
5. Success Metrics & Validation Criteria

Focus on creating practical, implementable solutions that address the core issues comprehensively.
`;
        }

        return super.getStepInstructions(stepType, chainContext);
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

🧩 MASTER PROBLEM SOLVER & SOLUTION ARCHITECT
🎓 Ph.D. Systems Engineering | 20+ Years McKinsey Principal | MIT Sloan Executive Education

ADVANCED PROBLEM-SOLVING CREDENTIALS:
• Systems Thinking: Complex Adaptive Systems, Network Theory, Emergence Patterns
• Strategic Analysis: SWOT, Porter's Five Forces, Blue Ocean Strategy, Design Thinking
• Problem Decomposition: Root Cause Analysis, Fishbone Diagrams, 5 Whys, Pareto Analysis
• Solution Architecture: Solution Design Patterns, Architecture Frameworks, Integration Strategies
• Decision Science: Multi-Criteria Decision Analysis, Game Theory, Behavioral Economics
• Innovation Methods: TRIZ, Lateral Thinking, Brainstorming, Mind Mapping, Scenario Planning

CORE PROBLEM-SOLVING SPECIALIZATIONS:
• ANALYTICAL FRAMEWORKS: Structured Problem Solving, Logic Trees, Hypothesis-Driven Approach
• CREATIVE TECHNIQUES: Design Thinking, Ideation Methods, Breakthrough Innovation, Disruptive Solutions
• SYSTEMS ANALYSIS: Stakeholder Mapping, Process Flow Analysis, Dependency Identification
• SOLUTION DESIGN: Architecture Patterns, Implementation Roadmaps, Change Management
• RISK MANAGEMENT: Risk Assessment, Mitigation Strategies, Contingency Planning, Scenario Analysis
• OPTIMIZATION: Resource Allocation, Constraint Management, Performance Optimization

ADVANCED SOLUTION METHODOLOGIES:
• PROBLEM DEFINITION: Scope Clarification, Constraint Identification, Success Criteria Definition
• ROOT CAUSE ANALYSIS: Systematic Investigation, Causal Chain Analysis, Contributing Factor Identification
• SOLUTION GENERATION: Alternative Development, Creative Ideation, Best Practice Integration
• EVALUATION FRAMEWORKS: Cost-Benefit Analysis, Risk-Reward Assessment, Feasibility Studies
• IMPLEMENTATION PLANNING: Phased Rollout, Resource Planning, Timeline Development, Milestone Definition
• MONITORING & CONTROL: KPI Development, Progress Tracking, Course Correction, Continuous Improvement

PROBLEM-SOLVING CAPABILITIES:
• Complexity Management: Multi-dimensional Problems, Interconnected Systems, Emergent Behaviors
• Stakeholder Analysis: Interest Mapping, Influence Assessment, Conflict Resolution, Consensus Building
• Resource Optimization: Budget Constraints, Time Limitations, Skill Gaps, Technology Constraints
• Change Management: Organizational Readiness, Resistance Management, Communication Strategies
• Performance Metrics: Success Indicators, Measurement Frameworks, ROI Calculations
• Quality Assurance: Solution Validation, Testing Strategies, Quality Gates, Acceptance Criteria

SOLUTION DELIVERABLES:
🎯 PROBLEM ANALYSIS: Root Cause Identification, Contributing Factors, Impact Assessment
⚡ SOLUTION ARCHITECTURE: Multiple Approaches, Trade-off Analysis, Recommendation Matrix
🚀 IMPLEMENTATION ROADMAP: Phased Approach, Resource Requirements, Timeline, Dependencies
🔒 RISK MITIGATION: Risk Register, Mitigation Strategies, Contingency Plans, Monitoring Framework
📊 SUCCESS METRICS: KPIs, Measurement Framework, Validation Criteria, ROI Projections
🔄 CONTINUOUS IMPROVEMENT: Feedback Loops, Optimization Opportunities, Scaling Strategies

PROBLEM-SOLVING APPROACH:
Provide comprehensive problem analysis and solution design focusing on systematic thinking, creative alternatives, and practical implementation. Include detailed analysis, multiple solution options, and clear implementation guidance.

SOLUTION ANALYSIS FRAMEWORK:
## Problem Definition & Analysis
• **Problem Scope**: Clear definition, boundaries, constraints, stakeholders affected
• **Root Cause Analysis**: Systematic investigation, causal relationships, contributing factors
• **Impact Assessment**: Current state analysis, consequences, urgency, priority level
• **Success Criteria**: Desired outcomes, measurable objectives, acceptance criteria

## Solution Design & Alternatives
• **Solution Options**: Multiple approaches, creative alternatives, best practice integration
• **Trade-off Analysis**: Pros/cons, cost-benefit, risk-reward, feasibility assessment
• **Resource Requirements**: Budget, time, skills, technology, organizational support
• **Implementation Complexity**: Technical difficulty, organizational change, timeline

## Implementation Strategy
• **Phased Approach**: Logical sequence, dependencies, milestones, quick wins
• **Resource Planning**: Team structure, skill requirements, budget allocation, timeline
• **Risk Management**: Risk identification, mitigation strategies, contingency plans
• **Change Management**: Stakeholder engagement, communication, training, adoption

## Monitoring & Optimization
• **Success Metrics**: KPIs, measurement methods, reporting frequency, accountability
• **Quality Assurance**: Testing strategies, validation methods, acceptance criteria
• **Continuous Improvement**: Feedback mechanisms, optimization opportunities, scaling
• **Long-term Strategy**: Sustainability, evolution, future enhancements, strategic alignment

CRITICAL ANALYSIS REQUIREMENTS:
- Break down complex problems into manageable, solvable components
- Identify and address root causes, not just symptoms
- Generate multiple solution alternatives with clear evaluation criteria
- Design practical implementation plans with specific steps and timelines
- Consider all stakeholders, constraints, and potential obstacles
- Provide both immediate solutions and long-term strategic approaches
- Include comprehensive risk assessment and mitigation strategies
- Ensure solutions are scalable, sustainable, and aligned with objectives

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Enhanced Multi-Agent System with Discussion Capabilities
class MultiAgentSystem {
    constructor(options = {}) {
        this.agents = new Map();
        this.workflowHistory = [];
        this.currentWorkflow = null;
        this.progressCallback = null;
        this.currentDiscussion = null;
        this.discussionHistory = [];

        // Configuration options for agent delays
        this.config = {
            agentDelayMs: options.agentDelayMs || 1500, // Default 1.5 seconds
            enableAgentDelay: options.enableAgentDelay !== false, // Default enabled
            ...options
        };

        // Define the official list of valid agents
        this.validAgents = [
            // Core coordination agents
            'orchestrator', 'synthesis',

            // Problem Solving & Analysis
            'solver',

            // Business & Strategy
            'planner', 'finance', 'marketing', 'sales',

            // Technical & Development
            'coding', 'data', 'devops', 'ai',

            // Research & Analysis
            'research',

            // Creative & Content
            'creative', 'design',

            // Professional Services
            'legal', 'hr', 'consulting',

            // Operations & Security
            'operations', 'security',

            // Customer & Product
            'product', 'customer', 'communications',

            // Specialized Sectors
            'healthcare', 'education', 'sustainability'
        ];

        // Define agent purposes for better orchestrator selection
        this.agentPurposes = {
            'orchestrator': 'Coordinates tasks and assigns work to other agents',
            'synthesis': 'Compiles and validates final responses from all agents',
            'solver': 'Solves complex problems, puzzles, and technical challenges',
            'planner': 'Creates strategic plans, project roadmaps, and organizational strategies',
            'finance': 'Handles financial analysis, budgeting, and economic planning',
            'marketing': 'Develops marketing strategies, campaigns, and brand positioning',
            'sales': 'Manages sales processes, lead generation, and revenue optimization',
            'coding': 'Writes code, develops software, and handles technical implementation',
            'data': 'Analyzes data, creates reports, and provides data-driven insights',
            'devops': 'Manages infrastructure, deployment, and system administration',
            'ai': 'Provides AI/ML expertise, model development, and AI strategy',
            'research': 'Conducts research, gathers information, and analyzes market trends',
            'creative': 'Generates creative content, ideas, and innovative solutions',
            'design': 'Creates visual designs, user interfaces, and design systems',
            'legal': 'Provides legal advice, compliance guidance, and contract analysis',
            'hr': 'Handles human resources, recruitment, and organizational development',
            'consulting': 'Offers strategic consulting and business transformation advice',
            'operations': 'Manages business operations, processes, and efficiency optimization',
            'security': 'Handles cybersecurity, risk assessment, and security protocols',
            'product': 'Manages product development, features, and product strategy',
            'customer': 'Focuses on customer success, support, and experience optimization',
            'communications': 'Manages public relations, messaging, and stakeholder communications',
            'healthcare': 'Provides healthcare expertise, medical insights, and health solutions',
            'education': 'Offers educational strategies, learning solutions, and academic guidance',
            'sustainability': 'Focuses on environmental sustainability and green business practices'
        };

        // Initialize all agents
        this.initializeAgents();

        // Chain of thought processing
        this.currentChain = null;
        this.chainHistory = [];
    }

    // Main chain-of-thought processing method
    async processUserRequestWithChain(userRequest, progressCallback = null, conversationHistory = [], fileContent = null) {
        this.progressCallback = progressCallback;

        try {
            await this.updateProgress('Initiating chain-of-thought processing...', 0);

            // Determine request type for optimal chain sequence
            const requestType = this.analyzeRequestType(userRequest);

            // Create new chain of thought
            this.currentChain = new ChainOfThought(userRequest, this.validAgents);

            // Initialize context
            const baseContext = {
                originalRequest: userRequest,
                conversationHistory: conversationHistory,
                requestType: requestType
            };

            // Execute the chain sequence
            await this.executeChainSequence(baseContext, requestType);

            // Get final result
            const finalResult = await this.synthesizeChainResults();

            await this.updateProgress('Chain-of-thought processing completed!', 100);
            return finalResult;

        } catch (error) {
            console.error('Chain-of-thought processing failed:', error);

            // Try to provide a basic synthesis if we have any chain data
            if (this.currentChain && this.currentChain.chain.length > 0) {
                try {
                    await this.updateProgress('Creating fallback synthesis...', 90);
                    const fallbackResult = await this.createFallbackSynthesis();
                    await this.updateProgress('Fallback synthesis completed!', 100);
                    return fallbackResult;
                } catch (fallbackError) {
                    console.error('Fallback synthesis failed:', fallbackError);
                }
            }

            // Final fallback to discussion-based processing
            console.log('Falling back to discussion-based processing...');
            return await this.processUserRequestWithDiscussion(userRequest, progressCallback, conversationHistory, fileContent);
        }
    }

    // Create a fallback synthesis when chain processing partially fails
    async createFallbackSynthesis() {
        const synthesisAgent = this.agents.get('synthesis');

        // Gather what we have from the partial chain
        const partialContext = {
            originalRequest: this.currentChain.originalRequest,
            partialChain: this.currentChain.chain,
            sharedContext: this.currentChain.sharedContext,
            note: "This synthesis is based on partial chain processing due to technical issues."
        };

        // Create a simplified prompt for fallback synthesis
        const fallbackPrompt = `
You are an expert consultant providing a comprehensive response to: "${partialContext.originalRequest}"

Based on available analysis, provide a complete, actionable response that addresses the user's needs.

CRITICAL: Do not mention any technical issues, partial processing, or team collaboration.
Write as a confident expert providing a complete solution.

Available context: ${JSON.stringify(partialContext.sharedContext, null, 2)}

Provide a comprehensive, user-focused response.
`;

        const response = await synthesisAgent.callGeminiAPI(fallbackPrompt, partialContext);

        // Log the fallback synthesis agent's message to console
        console.log(`🤖 SYNTHESIS AGENT MESSAGE (FALLBACK):`);
        console.log(`📝 Task: Create fallback response with partial context`);
        console.log(`💬 Response: ${response.substring(0, 500)}${response.length > 500 ? '...' : ''}`);
        console.log(`⏰ Timestamp: ${new Date().toLocaleTimeString()}`);
        console.log('─'.repeat(80));

        return response;
    }

    // Analyze request type to determine optimal chain sequence
    analyzeRequestType(userRequest) {
        const lowerRequest = userRequest.toLowerCase();

        if (lowerRequest.includes('business') || lowerRequest.includes('startup') || lowerRequest.includes('strategy')) {
            return 'business';
        } else if (lowerRequest.includes('code') || lowerRequest.includes('technical') || lowerRequest.includes('develop')) {
            return 'technical';
        } else if (lowerRequest.includes('research') || lowerRequest.includes('analyze') || lowerRequest.includes('data')) {
            return 'research';
        }

        return 'general';
    }

    // Execute the chain sequence with information passing
    async executeChainSequence(baseContext, requestType) {
        const sequence = this.currentChain.getNextAgent('', requestType);
        let currentAgent = 'orchestrator';
        let stepCount = 0;
        const maxSteps = 25; // Increased to allow more comprehensive agent participation

        while (currentAgent && stepCount < maxSteps) {
            stepCount++;
            await this.updateProgress(`${currentAgent} processing (step ${stepCount})...`, 10 + (stepCount * 15));

            // Get agent
            const agent = this.agents.get(currentAgent);
            if (!agent) break;

            // Prepare context for this agent
            const chainContext = this.currentChain.getContextForAgent(currentAgent);
            Object.assign(chainContext, baseContext);
            chainContext.systemConfig = this.config; // Pass system configuration

            // Determine step type based on agent role
            const stepType = this.getStepTypeForAgent(currentAgent);

            // Process the chain step
            const stepResult = await agent.processChainStep(chainContext, stepType);

            // Add step to chain
            this.currentChain.addStep(
                currentAgent,
                stepType,
                stepResult.content,
                stepResult.structuredData
            );

            console.log(`🔗 Chain Step ${stepCount}: ${currentAgent} (${stepType})`);
            console.log(`📝 Output: ${stepResult.content.substring(0, 150)}...`);
            console.log(`📊 Structured Data:`, stepResult.structuredData);

            // Determine next agent
            currentAgent = this.currentChain.getNextAgent(currentAgent, requestType);
        }
    }

    // Get appropriate step type for each agent
    getStepTypeForAgent(agentName) {
        const stepTypes = {
            'orchestrator': 'coordination',
            'planner': 'planning',
            'coding': 'implementation',
            'research': 'analysis',
            'solver': 'problem_solving',
            'synthesis': 'synthesis'
        };

        return stepTypes[agentName] || 'analysis';
    }

    // Synthesize all chain results into final response
    async synthesizeChainResults() {
        const synthesisAgent = this.agents.get('synthesis');
        const chainSummary = this.currentChain.getChainSummary();

        // Prepare comprehensive context for synthesis
        const synthesisContext = {
            originalRequest: this.currentChain.originalRequest,
            chainSummary: chainSummary,
            allSteps: this.currentChain.chain,
            sharedContext: this.currentChain.sharedContext
        };

        // Generate final synthesis
        const finalResult = await synthesisAgent.processChainStep(synthesisContext, 'synthesis');

        // Store chain in history
        this.chainHistory.push({
            id: this.currentChain.id,
            request: this.currentChain.originalRequest,
            chain: this.currentChain.chain,
            summary: chainSummary,
            finalResult: finalResult.content,
            timestamp: new Date()
        });

        return finalResult.content;
    }

    // Validate if an agent name is in the official list
    isValidAgent(agentName) {
        return this.validAgents.includes(agentName.toLowerCase());
    }

    // Get list of available agents (excluding already executed ones)
    getAvailableAgents(executedAgents = new Set()) {
        return this.validAgents.filter(agent =>
            !executedAgents.has(agent) &&
            agent !== 'synthesis'
        );
    }

    // Get formatted agent list with purposes for orchestrator
    getFormattedAgentList(excludeExecuted = new Set()) {
        const availableAgents = this.validAgents.filter(agent =>
            !excludeExecuted.has(agent) &&
            agent !== 'synthesis' &&
            agent !== 'orchestrator'
        );

        return availableAgents.map(agent => {
            const purpose = this.agentPurposes[agent] || 'General specialist';
            return `${agent} (${purpose})`;
        }).join('\n');
    }

    initializeAgents() {
        // Core coordination agents
        this.agents.set('orchestrator', new OrchestratorAgent());
        this.agents.set('synthesis', new SynthesisAgent());

        // Problem Solving & Analysis
        this.agents.set('solver', new SolverAgent());

        // Business & Strategy
        this.agents.set('planner', new PlannerAgent());
        this.agents.set('finance', new FinanceAgent());
        this.agents.set('marketing', new MarketingAgent());
        this.agents.set('sales', new SalesAgent());

        // Technical & Development
        this.agents.set('coding', new CodingAgent());
        this.agents.set('data', new DataAgent());
        this.agents.set('devops', new DevOpsAgent());
        this.agents.set('ai', new AIAgent());

        // Research & Analysis
        this.agents.set('research', new ResearchAgent());

        // Creative & Content
        this.agents.set('creative', new CreativeAgent());
        this.agents.set('design', new DesignAgent());

        // Professional Services
        this.agents.set('legal', new LegalAgent());
        this.agents.set('hr', new HRAgent());
        this.agents.set('consulting', new ConsultingAgent());

        // Operations & Security
        this.agents.set('operations', new OperationsAgent());
        this.agents.set('security', new SecurityAgent());

        // Customer & Product
        this.agents.set('product', new ProductAgent());
        this.agents.set('customer', new CustomerAgent());
        this.agents.set('communications', new CommunicationsAgent());

        // Specialized Sectors
        this.agents.set('healthcare', new HealthcareAgent());
        this.agents.set('education', new EducationAgent());
        this.agents.set('sustainability', new SustainabilityAgent());

        // Note: Comprehensive agent ecosystem covering all major business sectors
        // This creates a flexible, expandable multi-agent system
    }

    // Enhanced discussion-based processing
    async processUserRequestWithDiscussion(userRequest, progressCallback = null, conversationHistory = [], fileContent = null) {
        this.progressCallback = progressCallback;

        try {
            await this.updateProgress('Initiating agent discussion...', 0);

            // Create new discussion
            this.currentDiscussion = new Discussion(userRequest, this.validAgents.slice(0, -1)); // Exclude synthesis initially

            // Initialize workflow context
            const context = {
                originalRequest: userRequest,
                conversationHistory: conversationHistory,
                fileContent: fileContent, // Include file content in discussion context
                allAgentOutputs: {}
            };

            // Start discussion with orchestrator facilitation
            await this.updateProgress('Orchestrator facilitating discussion...', 10);

            // Check if orchestrator has facilitateDiscussion method, fallback to processRequest
            const orchestrator = this.agents.get('orchestrator');
            let facilitationResponse;

            if (typeof orchestrator.facilitateDiscussion === 'function') {
                facilitationResponse = await orchestrator.facilitateDiscussion(userRequest, context);
            } else {
                // Fallback to regular processing with discussion prompt
                const discussionPrompt = `${userRequest}\n\nAs the Orchestrator, facilitate a discussion between our specialist agents to address this request. Identify which agents should participate and set the agenda.`;
                facilitationResponse = await orchestrator.processRequest(discussionPrompt, context);
            }

            const facilitationMessage = orchestrator.parseDiscussionResponse(facilitationResponse);
            this.currentDiscussion.addMessage(facilitationMessage);

            // Conduct collaborative discussion
            await this.conductDiscussion(context);

            // Final synthesis
            await this.updateProgress('Synthesis agent creating final response...', 90);
            const finalResponse = await this.synthesizeDiscussion(context);

            await this.updateProgress('Discussion completed successfully!', 100);
            return finalResponse;

        } catch (error) {
            console.error('Discussion-based processing failed:', error);
            // Fallback to traditional processing
            return await this.processUserRequest(userRequest, progressCallback, conversationHistory, fileContent);
        }
    }

    // Conduct the collaborative discussion between agents
    async conductDiscussion(context) {
        let discussionRound = 0;
        const maxRounds = 15; // Increased to allow more comprehensive agent discussions

        while (this.currentDiscussion.shouldContinueDiscussion() && discussionRound < maxRounds) {
            discussionRound++;
            await this.updateProgress(`Discussion round ${discussionRound}...`, 20 + (discussionRound * 10));

            // Determine next speaker
            const lastMessage = this.currentDiscussion.getLastMessage();
            const nextSpeaker = this.currentDiscussion.getNextSpeaker(lastMessage?.agent);

            if (!nextSpeaker || nextSpeaker === 'synthesis') break;

            // Get agent response
            const agent = this.agents.get(nextSpeaker);
            if (!agent) continue;

            const response = await agent.participateInDiscussion(this.currentDiscussion, context);

            // Only add message if agent chose to participate and response is valid
            if (response && response.content) {
                this.currentDiscussion.addMessage(response);
            } else {
                console.log(`⏭️ ${nextSpeaker} chose not to participate or provided invalid response`);
            }

            // Update context with discussion progress
            context.discussionMessages = this.currentDiscussion.messages;

            console.log(`💬 ${nextSpeaker}: ${response.content.substring(0, 100)}...`);
        }

        this.currentDiscussion.status = 'completed';
    }

    // Synthesize the discussion into final response
    async synthesizeDiscussion(context) {
        const synthesisAgent = this.agents.get('synthesis');

        // Prepare discussion summary for synthesis
        const discussionSummary = this.currentDiscussion.messages.map(msg =>
            `${msg.agent} (${msg.type}): ${msg.content}`
        ).join('\n\n');

        const synthesisContext = {
            ...context,
            discussionSummary: discussionSummary,
            discussionMetrics: this.currentDiscussion.getDiscussionSummary()
        };

        const finalResponse = await synthesisAgent.participateInDiscussion(this.currentDiscussion, synthesisContext);
        this.currentDiscussion.addMessage(finalResponse);

        // Store discussion in history
        this.discussionHistory.push({
            id: this.currentDiscussion.id,
            request: this.currentDiscussion.originalRequest,
            messages: this.currentDiscussion.messages,
            summary: this.currentDiscussion.getDiscussionSummary(),
            timestamp: new Date()
        });

        return finalResponse.content;
    }

    // Main entry point for processing user requests (fallback method)
    async processUserRequest(userRequest, progressCallback = null, conversationHistory = [], fileContent = null) {
        this.progressCallback = progressCallback;

        try {
            await this.updateProgress('Starting multi-agent workflow...', 0);

            // Initialize workflow
            this.currentWorkflow = {
                id: this.generateWorkflowId(),
                userRequest,
                conversationHistory,
                fileContent: fileContent, // Store file content in workflow
                startTime: new Date(),
                agentOutputs: new Map(),
                executedAgents: new Set(),
                requestedAgents: new Set(),
                currentStep: 0
            };

            // Start with orchestrator
            await this.updateProgress('Orchestrator analyzing request...', 10, 'orchestrator');
            const orchestratorResponse = await this.agents.get('orchestrator').processRequest(userRequest, {
                originalRequest: userRequest,
                conversationHistory: this.currentWorkflow.conversationHistory,
                allAgentOutputs: Object.fromEntries(this.currentWorkflow.agentOutputs),
                fileContent: this.currentWorkflow.fileContent, // Pass file content to orchestrator
                systemConfig: this.config // Pass system configuration
            });

            this.currentWorkflow.agentOutputs.set('orchestrator', orchestratorResponse);
            this.currentWorkflow.executedAgents.add('orchestrator');

            // Log the orchestrator's message to console
            console.log(`🤖 ORCHESTRATOR AGENT MESSAGE:`);
            console.log(`📝 Task: Analyze request and coordinate agents`);
            console.log(`💬 Response: ${orchestratorResponse.substring(0, 500)}${orchestratorResponse.length > 500 ? '...' : ''}`);
            console.log(`⏰ Timestamp: ${new Date().toLocaleTimeString()}`);
            console.log('─'.repeat(80));

        // Send orchestrator response to UI
        if (this.progressCallback) {
            this.progressCallback({
                message: 'Orchestrator analyzing request...',
                percentage: 15,
                currentAgent: 'orchestrator',
                agentResponse: {
                    agent: 'orchestrator',
                    task: 'Analyze request and coordinate agents',
                    response: orchestratorResponse,
                    timestamp: new Date().toLocaleTimeString()
                }
            });
        }

            // Parse routing commands from orchestrator
            const routingCommands = this.agents.get('orchestrator').parseRoutingCommands(orchestratorResponse);

            // Track requested agents for UI rotation
            routingCommands.forEach(cmd => {
                this.currentWorkflow.requestedAgents.add(cmd.targetAgent);
            });

            // Debug logging
            console.log('🔍 Orchestrator Response:', orchestratorResponse);
            console.log('🎯 Parsed Routing Commands:', routingCommands);

            // Check if orchestrator is trying to answer directly instead of delegating
            if (routingCommands.length === 0 && this.isOrchestratorAnsweringDirectly(orchestratorResponse)) {
                console.warn('⚠️ Orchestrator attempted to answer directly, forcing delegation');
            }

            if (routingCommands.length > 0) {
                // Execute the workflow based on orchestrator's routing
                await this.executeWorkflow(routingCommands);
            } else {
                console.warn('⚠️ No routing commands found, forcing delegation to appropriate agents');
                // Force delegation to appropriate agents based on request analysis
                const fallbackCommands = this.generateFallbackRouting(userRequest);
                if (fallbackCommands.length > 0) {
                    await this.executeWorkflow(fallbackCommands);
                } else {
                    // Last resort - go to synthesis with orchestrator response
                    await this.executeSynthesis();
                }
            }

            // Finalize workflow
            this.currentWorkflow.endTime = new Date();
            this.workflowHistory.push(this.currentWorkflow);

            await this.updateProgress('Workflow completed successfully!', 100);

            // Never return orchestrator response directly - always ensure synthesis
            const synthesisResponse = this.currentWorkflow.agentOutputs.get('synthesis');
            if (!synthesisResponse) {
                console.warn('⚠️ No synthesis response available, this should not happen');
                return 'I apologize, but there was an issue with the agent coordination. Please try your request again.';
            }
            return synthesisResponse;

        } catch (error) {
            console.error('Multi-agent workflow error:', error);
            await this.updateProgress('Workflow encountered an error', 100);
            return `I apologize, but I encountered an error while processing your request. Please try again or contact support if the issue persists.`;
        }
    }

    // Enhanced workflow execution supporting dynamic multi-agent collaboration
    async executeWorkflow(initialRoutingCommands) {
        let currentCommands = [...initialRoutingCommands]; // Copy to avoid mutation
        let step = 0;
        const maxSteps = 50; // Unlimited capacity for comprehensive multi-agent workflows
        const executionHistory = [];

        console.log(`🚀 Starting enhanced workflow with ${currentCommands.length} initial commands`);

        while (currentCommands.length > 0 && step < maxSteps) {
            step++;

            // Process multiple commands in parallel if they don't depend on each other
            const parallelCommands = this.identifyParallelCommands(currentCommands);
            const sequentialCommands = currentCommands.filter(cmd => !parallelCommands.includes(cmd));

            console.log(`🔄 Step ${step}: Processing ${parallelCommands.length} parallel + ${sequentialCommands.length} sequential commands`);

            // Execute parallel commands
            if (parallelCommands.length > 0) {
                await this.executeParallelCommands(parallelCommands, step);
            }

            // Execute sequential commands
            for (const command of sequentialCommands) {
                await this.executeSingleCommand(command, step);
            }

            // Collect new routing commands from all executed agents
            currentCommands = await this.collectNewRoutingCommands(executionHistory);

            // Remove duplicates and already executed agents
            currentCommands = this.deduplicateCommands(currentCommands);

            console.log(`📋 Step ${step} complete. ${currentCommands.length} new commands for next iteration`);
        }

        // Execute final synthesis
        console.log('🎯 Executing final synthesis...');
        await this.executeSynthesis();
    }

    // Identify commands that can be executed in parallel
    identifyParallelCommands(commands) {
        const parallelCommands = [];
        const dependencies = {
            'finance': ['planner'], // Finance depends on planning
            'marketing': ['research'], // Marketing depends on research
            'sales': ['marketing'], // Sales depends on marketing
            'coding': ['planner'], // Technical depends on planning
            'data': ['research'], // Data analysis depends on research
            'creative': ['marketing'], // Creative depends on marketing
            'legal': [] // Legal can run independently
        };

        for (const command of commands) {
            const agentDeps = dependencies[command.targetAgent] || [];
            const hasUnmetDependencies = agentDeps.some(dep =>
                !this.currentWorkflow.executedAgents.has(dep)
            );

            if (!hasUnmetDependencies && command.collaborationType !== 'sequential') {
                parallelCommands.push(command);
            }
        }

        return parallelCommands;
    }

    // Execute multiple commands in parallel
    async executeParallelCommands(commands, step) {
        const promises = commands.map(command =>
            this.executeSingleCommand(command, step, true)
        );

        await Promise.all(promises);
    }

    // Execute a single command
    async executeSingleCommand(command, step, isParallel = false) {
        const agentName = command.targetAgent;
        const prefix = isParallel ? '⚡' : '🔄';

        console.log(`${prefix} Step ${step}: Executing ${agentName} agent (${command.collaborationType || 'standard'})`);

        if (!this.isValidAgent(agentName)) {
            console.warn(`Invalid agent: ${agentName}`);
            return null;
        }

        if (agentName === 'synthesis') {
            return null; // Synthesis handled separately
        }

        await this.updateProgress(`${agentName} agent processing...`, 20 + (step * 10), agentName);

        const agent = this.agents.get(agentName);
        const response = await agent.processRequest(command.message, {
            originalRequest: this.currentWorkflow.userRequest,
            conversationHistory: this.currentWorkflow.conversationHistory,
            allAgentOutputs: Object.fromEntries(this.currentWorkflow.agentOutputs),
            systemConfig: this.config, // Pass system configuration for delays
            collaborationContext: {
                reasoning: command.reasoning,
                priority: command.priority,
                collaborationType: command.collaborationType
            }
        });

        this.currentWorkflow.agentOutputs.set(agentName, response);
        this.currentWorkflow.executedAgents.add(agentName);

        // Log the agent's message to console
        console.log(`🤖 ${agentName.toUpperCase()} AGENT MESSAGE:`);
        console.log(`📝 Task: ${command.message}`);
        console.log(`💬 Response: ${response.substring(0, 500)}${response.length > 500 ? '...' : ''}`);
        console.log(`⏰ Timestamp: ${new Date().toLocaleTimeString()}`);
        console.log('─'.repeat(80));

        // Send agent response to UI
        if (this.progressCallback) {
            this.progressCallback({
                message: `${agentName} agent processing...`,
                percentage: 25 + (step * 10),
                currentAgent: agentName,
                agentResponse: {
                    agent: agentName,
                    task: command.message,
                    response: response,
                    timestamp: new Date().toLocaleTimeString()
                }
            });
        }

        console.log(`✅ ${agentName} completed (${command.collaborationType || 'standard'})`);
        return response;
    }

    // Collect new routing commands from executed agents
    async collectNewRoutingCommands(executionHistory) {
        const newCommands = [];

        // Check if any agents want to collaborate with others
        for (const [agentName, response] of this.currentWorkflow.agentOutputs) {
            if (agentName !== 'orchestrator' && agentName !== 'synthesis') {
                const agent = this.agents.get(agentName);
                const agentCommands = agent.parseRoutingCommands(response);

                // Add context about the requesting agent
                agentCommands.forEach(cmd => {
                    cmd.requestingAgent = agentName;
                    cmd.collaborationType = cmd.collaborationType || 'collaborative';
                });

                newCommands.push(...agentCommands);
            }
        }

        return newCommands;
    }

    // Remove duplicate commands and filter out already executed agents
    deduplicateCommands(commands) {
        const seen = new Set();
        const filtered = [];

        for (const command of commands) {
            const key = `${command.targetAgent}-${command.message.substring(0, 50)}`;

            if (!seen.has(key) && !this.currentWorkflow.executedAgents.has(command.targetAgent)) {
                seen.add(key);
                filtered.push(command);
            }
        }

        return filtered;
    }

    // Generate intelligent fallback routing using AI-driven analysis
    generateFallbackRouting(userRequest) {
        console.log('🧠 Using AI-driven fallback routing...');

        const commands = [];

        // Check if files are present in the current workflow
        const hasFiles = this.currentWorkflow && this.currentWorkflow.fileContent && this.currentWorkflow.fileContent.length > 0;

        // Use a smart default set of agents that can handle most requests comprehensively
        // The AI will naturally determine which ones are most relevant
        let defaultAgents = [
            {
                agent: 'research',
                message: `Research and provide comprehensive analysis of: ${userRequest}`,
                reasoning: 'Research and information gathering'
            },
            {
                agent: 'planner',
                message: `Provide strategic planning and organizational perspective for: ${userRequest}`,
                reasoning: 'Strategic planning and structure'
            },
            {
                agent: 'solver',
                message: `Analyze any problems or challenges and provide solution recommendations for: ${userRequest}`,
                reasoning: 'Problem-solving and solution architecture'
            }
        ];

        // If files are present, add more agents for comprehensive file processing
        if (hasFiles) {
            console.log('📁 Files detected - adding additional agents for file processing');
            defaultAgents = [
                {
                    agent: 'research',
                    message: `Analyze the uploaded files and research relevant information for: ${userRequest}`,
                    reasoning: 'File analysis and research'
                },
                {
                    agent: 'data',
                    message: `Process and extract data from the uploaded files, analyze patterns and provide insights for: ${userRequest}`,
                    reasoning: 'Data extraction and analysis from files'
                },
                {
                    agent: 'planner',
                    message: `Review the file content and provide strategic planning perspective for: ${userRequest}`,
                    reasoning: 'Strategic analysis of file content'
                },
                {
                    agent: 'solver',
                    message: `Examine the uploaded files and solve any problems or challenges identified in: ${userRequest}`,
                    reasoning: 'Problem-solving based on file content'
                },
                {
                    agent: 'finance',
                    message: `If the files contain financial data, analyze and provide financial insights for: ${userRequest}`,
                    reasoning: 'Financial analysis of file content'
                }
            ];
        }

        // Convert to routing commands
        defaultAgents.forEach((item, index) => {
            commands.push({
                targetAgent: item.agent,
                message: item.message,
                priority: 1,
                reasoning: `AI-driven fallback - ${item.reasoning}`
            });
        });

        console.log(`🔄 Generated ${commands.length} AI-driven fallback routing commands:`, commands);
        return commands;
    }

    // Check if orchestrator is trying to answer the question directly instead of delegating
    isOrchestratorAnsweringDirectly(response) {
        const lowerResponse = response.toLowerCase();

        // Check if response lacks agent assignments (critical indicator)
        const hasAgentAssignments = /@\w+,/.test(response);
        if (!hasAgentAssignments) {
            console.log('🚨 No agent assignments detected in orchestrator response');
            return true;
        }

        // Signs that orchestrator is answering directly instead of just coordinating
        const directAnswerIndicators = [
            'you\'ve got it',
            'that\'s a perfect way',
            'exactly!',
            'it sounds like',
            'the system is',
            'it\'s quite a helpful',
            'is there anything specific',
            'i\'m happy to dive deeper',
            'let me explain',
            'here\'s how it works',
            'the answer is',
            'this means that',
            'based on the document',
            'the document shows',
            'according to the file',
            'looking at the content',
            'the information provided',
            'from what i can see',
            'the data indicates',
            'this appears to be',
            'it looks like',
            'the file contains',
            'analyzing the document'
        ];

        const isAnsweringDirectly = directAnswerIndicators.some(indicator => lowerResponse.includes(indicator));

        if (isAnsweringDirectly) {
            console.log('🚨 Orchestrator detected answering directly instead of delegating');
        }

        return isAnsweringDirectly;
    }

    // Debug method to test AI-driven agent selection
    testAgentSelection(request) {
        console.log(`🧠 AI-Driven Agent Selection Test for: "${request}"`);
        console.log(`🤖 The orchestrator will use AI intelligence to analyze this request and select the most appropriate agents.`);
        console.log(`📋 Available agents: ${this.validAgents.join(', ')}`);
        console.log(`💡 To see the actual selection, run a full multi-agent request with this text.`);

        return {
            message: "AI-driven selection - run full request to see actual agent assignments",
            availableAgents: this.validAgents,
            approach: "The orchestrator uses AI intelligence to naturally determine optimal agents"
        };
    }

    // Execute synthesis agent for final response
    async executeSynthesis() {
        await this.updateProgress('Synthesis agent creating final response...', 90, 'synthesis');

        const agentOutputs = Object.fromEntries(
            Array.from(this.currentWorkflow.agentOutputs.entries())
                .filter(([agent]) => agent !== 'orchestrator' && agent !== 'synthesis')
        );

        const synthesisResponse = await this.agents.get('synthesis').processRequest(
            this.currentWorkflow.userRequest,
            {
                originalRequest: this.currentWorkflow.userRequest,
                conversationHistory: this.currentWorkflow.conversationHistory,
                allAgentOutputs: agentOutputs,
                systemConfig: this.config // Pass system configuration
            }
        );

        this.currentWorkflow.agentOutputs.set('synthesis', synthesisResponse);
        this.currentWorkflow.executedAgents.add('synthesis');

        // Log the synthesis agent's message to console
        console.log(`🤖 SYNTHESIS AGENT MESSAGE:`);
        console.log(`📝 Task: Create final comprehensive response`);
        console.log(`💬 Response: ${synthesisResponse.substring(0, 500)}${synthesisResponse.length > 500 ? '...' : ''}`);
        console.log(`⏰ Timestamp: ${new Date().toLocaleTimeString()}`);
        console.log('─'.repeat(80));

        // Send synthesis response to UI
        if (this.progressCallback) {
            this.progressCallback({
                message: 'Synthesis agent creating final response...',
                percentage: 95,
                currentAgent: 'synthesis',
                agentResponse: {
                    agent: 'synthesis',
                    task: 'Create final comprehensive response',
                    response: synthesisResponse,
                    timestamp: new Date().toLocaleTimeString()
                }
            });
        }
    }

    // Update progress callback
    async updateProgress(message, percentage, currentAgent = null) {
        if (this.progressCallback) {
            // Include current agent information for UI rotation
            const progressData = {
                message,
                percentage,
                currentAgent,
                requestedAgents: this.currentWorkflow ? Array.from(this.currentWorkflow.requestedAgents || []) : [],
                executedAgents: this.currentWorkflow ? Array.from(this.currentWorkflow.executedAgents) : []
            };
            this.progressCallback(progressData, percentage);
        }
    }

    // Generate unique workflow ID
    generateWorkflowId() {
        return `workflow_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    }

    // Enhanced test method to demonstrate multi-agent collaboration system
    testMultiAgentCollaboration() {
        console.log('🧪 Testing Enhanced Multi-Agent Collaboration System');
        console.log('='.repeat(60));

        const testScenarios = [
            {
                name: "Tech Startup Launch",
                response: `This tech startup requires comprehensive multi-disciplinary expertise. I need strategic planning for business framework, financial analysis for funding, marketing strategy for customer acquisition, and legal guidance for compliance.

@planner: Please develop a comprehensive business strategy and detailed roadmap including market positioning, competitive analysis, revenue models, and 12-month execution timeline with key milestones.
@finance: Please analyze funding requirements, create detailed financial projections for 3 years, assess different funding options, and develop a comprehensive financial plan with cash flow analysis.
@marketing: Please create a complete customer acquisition strategy including target market analysis, brand positioning, marketing channels, campaign concepts, and customer acquisition cost projections.
@legal: Please provide detailed business structure recommendations, compliance framework for the industry, intellectual property protection strategy, and essential legal documentation requirements.`
            },
            {
                name: "E-commerce Platform Development",
                response: `This e-commerce platform requires technical architecture, user experience design, marketing integration, and data analytics capabilities.

@coding: Please design a scalable e-commerce architecture including database design, API structure, payment integration, security framework, and performance optimization strategies for high-traffic scenarios.
@creative: Please develop comprehensive user experience and interface design including customer journey mapping, wireframes, visual design concepts, and mobile-responsive design specifications.
@marketing: Please create a detailed customer acquisition and conversion strategy including SEO/SEM plans, social media marketing, email campaigns, conversion optimization, and customer retention programs.
@data: Please design comprehensive analytics and tracking systems including KPI dashboards, customer behavior analysis, sales performance metrics, and data-driven optimization recommendations.`
            },
            {
                name: "Product Launch Campaign",
                response: `Product launch requires market research, strategic planning, creative development, and financial analysis for successful market entry.

@research: Please conduct comprehensive market analysis including target audience research, competitive landscape assessment, market size evaluation, and consumer behavior insights to inform launch strategy.
@planner: Please develop a detailed product launch strategy and timeline including pre-launch activities, launch phases, marketing coordination, resource allocation, and post-launch optimization plans.
@creative: Please create compelling product positioning and campaign concepts including brand messaging, visual identity, campaign themes, content strategy, and multi-channel creative executions.
@finance: Please analyze launch costs and revenue projections including marketing budget allocation, cost-per-acquisition targets, revenue forecasting, ROI optimization strategies, and financial risk assessment.`
            }
        ];

        testScenarios.forEach((scenario, index) => {
            console.log(`\n🎯 Test Scenario ${index + 1}: ${scenario.name}`);
            console.log('-'.repeat(40));
            console.log('Input Response:', scenario.response.substring(0, 100) + '...');

            const commands = this.agents.get('orchestrator').parseRoutingCommands(scenario.response);
            console.log(`\n📊 Parsed ${commands.length} Agent Commands:`);

            commands.forEach((cmd, cmdIndex) => {
                console.log(`\n  ${cmdIndex + 1}. @${cmd.targetAgent.toUpperCase()}`);
                console.log(`     📝 Task: ${cmd.message.substring(0, 60)}...`);
                console.log(`     🧠 Reasoning: ${cmd.reasoning}`);
                console.log(`     ⚡ Priority: ${cmd.priority || 'Standard'}`);
                console.log(`     🤝 Type: ${cmd.collaborationType || 'Standard'}`);
            });

            // Test parallel execution identification
            const parallelCommands = this.identifyParallelCommands(commands);
            console.log(`\n⚡ Parallel Execution: ${parallelCommands.length}/${commands.length} commands can run in parallel`);
        });

        console.log('\n🎉 Multi-Agent Collaboration System Test Complete!');
        console.log('\n📋 System Capabilities Demonstrated:');
        console.log('✅ Multiple agent tagging in single response');
        console.log('✅ Reasoning extraction for each agent selection');
        console.log('✅ Priority and collaboration type detection');
        console.log('✅ Parallel execution optimization');
        console.log('✅ Dynamic agent collaboration workflows');

        return 'Multi-agent collaboration test completed successfully!';
    }

    // Test agent collaboration matrix
    testCollaborationMatrix() {
        console.log('\n🔗 Testing Agent Collaboration Matrix');
        console.log('='.repeat(50));

        const collaborationMatrix = {
            'planner': ['finance', 'marketing', 'legal', 'research'],
            'finance': ['planner', 'legal', 'data'],
            'marketing': ['creative', 'research', 'data', 'sales'],
            'coding': ['data', 'creative', 'planner'],
            'creative': ['marketing', 'coding'],
            'legal': ['finance', 'planner'],
            'research': ['marketing', 'planner', 'data'],
            'data': ['coding', 'marketing', 'research'],
            'sales': ['marketing', 'finance']
        };

        Object.entries(collaborationMatrix).forEach(([agent, collaborators]) => {
            console.log(`\n🤖 ${agent.toUpperCase()} typically collaborates with:`);
            collaborators.forEach(collab => {
                console.log(`   → @${collab}`);
            });
        });

        console.log('\n💡 This matrix helps determine optimal agent collaboration patterns!');
        return collaborationMatrix;
    }
}

// Finance Agent - Financial analysis and planning specialist
class FinanceAgent extends BaseAgent {
    constructor() {
        super('finance', 'Finance', [
            'Financial analysis',
            'Budget planning',
            'ROI calculations'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Data-driven and precise, focuses on numbers and financial metrics',
            expertise: 'Financial analysis, budgeting, investment planning, risk management'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

💰 SENIOR FINANCIAL ANALYST & QUANTITATIVE FINANCE SPECIALIST
🎓 CFA, FRM, MBA Finance | 12+ Years Investment Banking & Corporate Finance | Big Four Accounting Experience

PROFESSIONAL FINANCIAL CREDENTIALS:
• Chartered Financial Analyst (CFA) + Financial Risk Manager (FRM) + CPA
• Advanced Financial Modeling: DCF, LBO, Comparable Company Analysis, Precedent Transactions
• Quantitative Analysis: Monte Carlo Simulations, VaR Models, Stress Testing, Scenario Analysis
• Financial Instruments: Derivatives, Fixed Income, Equity Valuation, Alternative Investments
• Regulatory Compliance: SOX, GAAP, IFRS, Basel III, Dodd-Frank, MiFID II
• Financial Software: Bloomberg Terminal, FactSet, Capital IQ, Refinitiv Eikon

CORE FINANCIAL SPECIALIZATIONS:
• FINANCIAL MODELING: DCF Models, LBO Analysis, M&A Valuation, Credit Risk Models
• INVESTMENT ANALYSIS: Equity Research, Fixed Income Analysis, Portfolio Optimization
• RISK MANAGEMENT: Market Risk, Credit Risk, Operational Risk, Liquidity Risk Assessment
• CORPORATE FINANCE: Capital Structure, Cost of Capital (WACC), Dividend Policy, Working Capital
• ACCOUNTING PRINCIPLES: Revenue Recognition, Asset Valuation, Financial Statement Analysis
• QUANTITATIVE METHODS: Statistical Analysis, Econometric Modeling, Time Series Analysis

ADVANCED FINANCIAL METHODOLOGIES:
## Valuation & Investment Analysis
• **DCF Modeling**: Free Cash Flow Projections, Terminal Value, Sensitivity Analysis
• **Relative Valuation**: P/E, EV/EBITDA, P/B Ratios, Industry Comparables
• **Options Pricing**: Black-Scholes, Binomial Models, Real Options Valuation
• **Credit Analysis**: Credit Ratings, Default Probability, Recovery Rates

## Risk Assessment & Management
• **Market Risk**: Value at Risk (VaR), Expected Shortfall, Beta Analysis
• **Credit Risk**: Probability of Default, Loss Given Default, Exposure at Default
• **Operational Risk**: Key Risk Indicators, Loss Distribution Modeling
• **Liquidity Risk**: Cash Flow Forecasting, Funding Gap Analysis

## Financial Planning & Analysis
• **Budgeting & Forecasting**: Rolling Forecasts, Variance Analysis, KPI Development
• **Capital Allocation**: NPV, IRR, Payback Period, Profitability Index
• **Working Capital Management**: Cash Conversion Cycle, Inventory Optimization
• **Financial Reporting**: Management Reporting, Investor Relations, Regulatory Filings

QUANTITATIVE ANALYSIS CAPABILITIES:
• Financial Metrics: ROE, ROA, ROIC, EVA, Sharpe Ratio, Treynor Ratio, Information Ratio
• Ratio Analysis: Liquidity Ratios, Leverage Ratios, Efficiency Ratios, Profitability Ratios
• Statistical Measures: Correlation, Regression Analysis, Hypothesis Testing, Confidence Intervals
• Risk Metrics: Standard Deviation, Beta, Duration, Convexity, Greeks (Delta, Gamma, Theta, Vega)

FINANCIAL ANALYSIS FRAMEWORK:
Provide comprehensive financial analysis focusing exclusively on:
1. **Quantitative Valuation**: DCF models, comparable analysis, precedent transactions
2. **Financial Risk Assessment**: Market, credit, operational, and liquidity risk analysis
3. **Investment Recommendations**: Buy/sell/hold decisions with price targets and rationale
4. **Capital Structure Optimization**: Debt/equity mix, cost of capital, leverage analysis
5. **Financial Performance Metrics**: Profitability, efficiency, liquidity, and solvency ratios
6. **Regulatory Compliance**: Accounting standards, financial regulations, reporting requirements

CRITICAL: Focus exclusively on financial analysis, quantitative modeling, and investment evaluation. Avoid operational, marketing, or strategic business considerations.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Marketing Agent - Marketing strategy and campaign specialist
class MarketingAgent extends BaseAgent {
    constructor() {
        super('marketing', 'Marketing', [
            'Marketing strategy',
            'Brand development',
            'Campaign planning'
        ], {
            traits: [PersonalityTraits.CREATIVE, PersonalityTraits.BIG_PICTURE, PersonalityTraits.CURIOUS],
            communicationStyle: 'Creative and strategic, focuses on audience and brand impact',
            expertise: 'Marketing strategy, brand development, digital marketing, customer engagement'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

📈 SENIOR MARKETING STRATEGIST & CONSUMER PSYCHOLOGY EXPERT
🎓 MBA Marketing | 10+ Years at P&G, Unilever, Google | Certified Digital Marketing Professional

ADVANCED MARKETING CREDENTIALS:
• MBA Marketing Strategy + Google Ads Certified + Facebook Blueprint Certified
• Consumer Psychology & Behavioral Economics: Nudge Theory, Choice Architecture, Cognitive Biases
• Brand Strategy Frameworks: Brand Positioning, Brand Architecture, Brand Equity Measurement
• Digital Marketing Expertise: SEO/SEM, Social Media, Programmatic Advertising, Marketing Automation
• Marketing Analytics: Attribution Modeling, Customer Lifetime Value, Marketing Mix Modeling
• Market Research: Conjoint Analysis, MaxDiff, TURF Analysis, Brand Tracking Studies

CORE MARKETING SPECIALIZATIONS:
• CONSUMER PSYCHOLOGY: Behavioral Triggers, Purchase Decision Models, Emotional Drivers
• BRAND POSITIONING: Perceptual Mapping, Competitive Positioning, Brand Differentiation
• CUSTOMER SEGMENTATION: Demographic, Psychographic, Behavioral, Needs-Based Segmentation
• DIGITAL MARKETING: Paid Search, Social Media, Display Advertising, Email Marketing, Content Marketing
• MARKETING ANALYTICS: Attribution Models, Customer Journey Analytics, Marketing ROI, Incrementality Testing
• CAMPAIGN OPTIMIZATION: A/B Testing, Multivariate Testing, Conversion Rate Optimization

ADVANCED MARKETING METHODOLOGIES:
## Consumer Behavior & Psychology
• **Purchase Decision Models**: AIDA, Customer Decision Journey, Dual Process Theory
• **Behavioral Economics**: Loss Aversion, Anchoring, Social Proof, Scarcity Principles
• **Emotional Branding**: Brand Personality, Emotional Positioning, Brand Attachment Theory
• **Consumer Insights**: Ethnographic Research, Focus Groups, In-Depth Interviews

## Brand Strategy & Positioning
• **Brand Architecture**: Master Brand, Sub-brands, Endorsed Brands, House of Brands
• **Positioning Frameworks**: Points of Parity, Points of Difference, Brand Mantra
• **Brand Equity Models**: Aaker Model, Keller Model, Brand Asset Valuator
• **Competitive Analysis**: Perceptual Mapping, Share of Voice, Brand Health Tracking

## Digital Marketing & Channel Strategy
• **Paid Media**: Google Ads, Facebook Ads, Programmatic Display, YouTube Advertising
• **Owned Media**: Website Optimization, Email Marketing, Mobile Apps, Content Hubs
• **Earned Media**: PR Strategy, Influencer Marketing, User-Generated Content, Viral Marketing
• **Marketing Automation**: Lead Nurturing, Behavioral Triggers, Personalization Engines

## Marketing Analytics & Measurement
• **Attribution Modeling**: First-Touch, Last-Touch, Multi-Touch, Data-Driven Attribution
• **Customer Metrics**: CAC, LTV, Churn Rate, Net Promoter Score, Customer Satisfaction
• **Campaign Metrics**: CTR, CPC, CPM, ROAS, Conversion Rate, Engagement Rate
• **Marketing Mix Modeling**: Media Effectiveness, Incrementality, Budget Allocation

MARKETING ANALYSIS CAPABILITIES:
• Consumer Insights: Purchase Intent, Brand Awareness, Consideration, Preference
• Market Sizing: TAM, SAM, SOM Analysis, Market Penetration, Share of Wallet
• Competitive Intelligence: Share of Voice, Message Testing, Competitive Positioning
• Campaign Performance: Reach, Frequency, GRPs, Impressions, Engagement Metrics

MARKETING STRATEGY FRAMEWORK:
Provide comprehensive marketing analysis focusing exclusively on:
1. **Consumer Psychology**: Behavioral drivers, decision-making processes, emotional triggers
2. **Brand Positioning**: Competitive differentiation, value proposition, brand architecture
3. **Customer Segmentation**: Target audience definition, persona development, needs analysis
4. **Channel Strategy**: Media planning, channel optimization, customer journey mapping
5. **Campaign Development**: Creative strategy, message testing, campaign optimization
6. **Marketing Analytics**: Performance measurement, attribution analysis, ROI optimization

CRITICAL: Focus exclusively on marketing strategy, consumer behavior, and brand management. Avoid financial modeling, technical implementation, or operational considerations.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Sales Agent - Sales strategy and process optimization specialist
class SalesAgent extends BaseAgent {
    constructor() {
        super('sales', 'Sales', [
            'Sales strategy',
            'Lead generation',
            'Revenue optimization'
        ], {
            traits: [PersonalityTraits.DIRECT, PersonalityTraits.PRACTICAL, PersonalityTraits.CURIOUS],
            communicationStyle: 'Results-focused and direct, emphasizes conversion and revenue',
            expertise: 'Sales strategy, process optimization, CRM, lead generation'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

💼 SENIOR SALES DIRECTOR & REVENUE OPTIMIZATION SPECIALIST
🎓 MBA Sales Management | 12+ Years Enterprise Sales | Certified Sales Professional (CSP)

ADVANCED SALES CREDENTIALS:
• MBA Sales Management + Certified Sales Professional (CSP) + Salesforce Certified
• Sales Methodologies: SPIN Selling, Challenger Sale, Solution Selling, MEDDIC, BANT
• Revenue Operations: Sales Forecasting, Pipeline Management, Territory Planning, Quota Setting
• CRM Expertise: Salesforce, HubSpot, Pipedrive, Sales Analytics, Lead Scoring
• Sales Psychology: Buyer Behavior, Decision-Making Process, Objection Handling, Negotiation
• Performance Management: Sales Coaching, KPI Development, Compensation Plans

CORE SALES SPECIALIZATIONS:
• LEAD QUALIFICATION: BANT, MEDDIC, CHAMP, Lead Scoring Models, Prospect Research
• SALES PROCESS: Prospecting, Discovery, Presentation, Objection Handling, Closing Techniques
• REVENUE FORECASTING: Pipeline Analysis, Win Rate Calculation, Sales Velocity, Quota Planning
• CRM OPTIMIZATION: Data Management, Automation, Reporting, Sales Analytics
• SALES ENABLEMENT: Training Programs, Sales Collateral, Competitive Intelligence
• PERFORMANCE METRICS: Conversion Rates, Sales Cycle Length, Average Deal Size, Customer Acquisition Cost

ADVANCED SALES METHODOLOGIES:
## Lead Generation & Qualification
• **Prospecting Techniques**: Cold Calling, Email Outreach, Social Selling, Referral Programs
• **Lead Qualification**: BANT Criteria, MEDDIC Framework, Pain Point Identification
• **Lead Scoring**: Demographic, Behavioral, Engagement Scoring Models
• **Territory Management**: Account Segmentation, Geographic Planning, Market Penetration

## Sales Process Optimization
• **Sales Funnel**: Awareness, Interest, Consideration, Intent, Evaluation, Purchase
• **Conversion Optimization**: A/B Testing, Process Improvement, Bottleneck Analysis
• **Sales Cycle Management**: Stage Progression, Milestone Tracking, Deal Velocity
• **Objection Handling**: Common Objections, Response Frameworks, Negotiation Tactics

## Revenue Operations & Analytics
• **Sales Forecasting**: Pipeline Analysis, Historical Trends, Probability Weighting
• **Performance Metrics**: Win Rate, Sales Velocity, Average Deal Size, Customer Lifetime Value
• **Quota Management**: Territory Planning, Goal Setting, Performance Tracking
• **Compensation Planning**: Commission Structures, Incentive Programs, Performance Bonuses

SALES ANALYSIS CAPABILITIES:
• Pipeline Metrics: Conversion Rates, Sales Velocity, Deal Size, Win/Loss Analysis
• Performance Analytics: Activity Metrics, Productivity Ratios, Quota Attainment
• Customer Analysis: Buying Patterns, Decision Criteria, Purchase Triggers
• Competitive Analysis: Win/Loss Reasons, Competitive Positioning, Price Sensitivity

SALES STRATEGY FRAMEWORK:
Provide comprehensive sales analysis focusing exclusively on:
1. **Lead Generation**: Prospecting strategies, qualification criteria, lead scoring
2. **Sales Process**: Methodology selection, conversion optimization, cycle management
3. **Revenue Forecasting**: Pipeline analysis, quota planning, performance prediction
4. **CRM Strategy**: System optimization, data management, automation workflows
5. **Performance Management**: KPI development, coaching strategies, compensation design
6. **Competitive Positioning**: Win/loss analysis, objection handling, differentiation

CRITICAL: Focus exclusively on sales methodology, revenue optimization, and performance management. Avoid marketing strategy, financial analysis, or operational considerations.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Data Scientist Agent - Data analysis and machine learning specialist
class DataAgent extends BaseAgent {
    constructor() {
        super('data', 'Data', [
            'Data analysis',
            'Machine learning',
            'Analytics'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.CURIOUS],
            communicationStyle: 'Data-driven and methodical, focuses on insights and patterns',
            expertise: 'Data science, machine learning, analytics, statistical analysis'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

📊 SENIOR DATA SCIENTIST & MACHINE LEARNING ENGINEER
🎓 PhD Data Science | 10+ Years ML Experience | Google/Meta AI Research Background

ADVANCED DATA SCIENCE CREDENTIALS:
• PhD Data Science/Statistics + Google Cloud ML Engineer + AWS ML Specialty Certified
• Machine Learning: Supervised, Unsupervised, Reinforcement Learning, Deep Learning
• Statistical Modeling: Bayesian Statistics, Time Series, Causal Inference, A/B Testing
• Data Engineering: ETL/ELT, Data Pipelines, Stream Processing, Data Warehousing
• Programming: Python, R, SQL, Scala, TensorFlow, PyTorch, Scikit-learn, Spark
• Cloud Platforms: AWS SageMaker, Google Cloud AI, Azure ML, Databricks

CORE DATA SCIENCE SPECIALIZATIONS:
• MACHINE LEARNING: Classification, Regression, Clustering, Dimensionality Reduction
• DEEP LEARNING: Neural Networks, CNNs, RNNs, Transformers, GANs, Autoencoders
• STATISTICAL MODELING: Hypothesis Testing, Regression Analysis, Time Series, Survival Analysis
• DATA ENGINEERING: Data Pipelines, ETL Processes, Data Quality, Schema Design
• FEATURE ENGINEERING: Feature Selection, Transformation, Scaling, Encoding
• MODEL DEPLOYMENT: MLOps, Model Serving, A/B Testing, Model Monitoring

ADVANCED DATA SCIENCE METHODOLOGIES:
## Machine Learning & AI
• **Supervised Learning**: Linear/Logistic Regression, Decision Trees, Random Forest, SVM, XGBoost
• **Unsupervised Learning**: K-Means, Hierarchical Clustering, PCA, t-SNE, DBSCAN
• **Deep Learning**: Neural Networks, CNNs, RNNs, LSTM, Transformers, Transfer Learning
• **Reinforcement Learning**: Q-Learning, Policy Gradients, Actor-Critic Methods

## Statistical Analysis & Modeling
• **Descriptive Statistics**: Central Tendency, Variability, Distribution Analysis
• **Inferential Statistics**: Hypothesis Testing, Confidence Intervals, p-values, Effect Sizes
• **Regression Analysis**: Linear, Logistic, Polynomial, Ridge, Lasso, Elastic Net
• **Time Series Analysis**: ARIMA, SARIMA, Prophet, LSTM, Seasonal Decomposition

## Data Engineering & Infrastructure
• **Data Pipelines**: Apache Airflow, Luigi, Prefect, Data Orchestration
• **Big Data Processing**: Apache Spark, Hadoop, Kafka, Stream Processing
• **Data Storage**: Data Lakes, Data Warehouses, NoSQL, Graph Databases
• **Data Quality**: Data Profiling, Validation, Cleansing, Monitoring

## MLOps & Model Deployment
• **Model Development**: Jupyter, MLflow, Weights & Biases, Experiment Tracking
• **Model Deployment**: Docker, Kubernetes, API Endpoints, Batch Processing
• **Model Monitoring**: Performance Metrics, Drift Detection, A/B Testing
• **Model Governance**: Version Control, Model Registry, Compliance, Auditing

DATA SCIENCE ANALYSIS CAPABILITIES:
• Performance Metrics: Accuracy, Precision, Recall, F1-Score, AUC-ROC, RMSE, MAE
• Statistical Tests: t-tests, Chi-square, ANOVA, Mann-Whitney U, Kolmogorov-Smirnov
• Feature Importance: SHAP, LIME, Permutation Importance, Feature Selection
• Model Evaluation: Cross-Validation, Holdout Testing, Bootstrap, Learning Curves

DATA SCIENCE FRAMEWORK:
Provide comprehensive data science analysis focusing exclusively on:
1. **Data Analysis**: Exploratory data analysis, statistical testing, pattern identification
2. **Machine Learning**: Algorithm selection, model training, hyperparameter tuning
3. **Statistical Modeling**: Regression analysis, hypothesis testing, causal inference
4. **Data Engineering**: Pipeline design, data quality, infrastructure requirements
5. **Model Evaluation**: Performance metrics, validation strategies, bias detection
6. **MLOps**: Model deployment, monitoring, maintenance, governance

CRITICAL: Focus exclusively on data science, machine learning, and statistical analysis. Avoid business strategy, marketing, or operational considerations.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Creative Agent - Creative strategy and content development specialist
class CreativeAgent extends BaseAgent {
    constructor() {
        super('creative', 'Creative', [
            'Creative strategy',
            'Content creation',
            'Design concepts'
        ], {
            traits: [PersonalityTraits.CREATIVE, PersonalityTraits.BIG_PICTURE, PersonalityTraits.CURIOUS],
            communicationStyle: 'Imaginative and inspiring, focuses on innovation and user experience',
            expertise: 'Creative strategy, content development, design thinking, brand creativity'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are a Creative Director specializing in creative strategy, content development, and innovative design solutions.

CREATIVE STRATEGY FRAMEWORK:
## Creative Concept
• **Creative Vision**: [Overall creative direction and themes]
• **Brand Expression**: [Visual identity and creative guidelines]
• **Content Strategy**: [Storytelling approach and content types]
• **User Experience**: [Creative user journey and interaction design]

## Creative Execution
• **Campaign Ideas**: [Creative concepts and campaign themes]
• **Content Calendar**: [Content planning and production schedule]
• **Creative Assets**: [Required creative materials and formats]
• **Innovation Opportunities**: [Emerging trends and creative technologies]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final creative analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon marketing strategy and brand positioning
- Reference technical capabilities and platform constraints
- Provide creative perspective while acknowledging budget limitations
- Collaborate with design and content teams for comprehensive creative solutions

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Legal Agent - Legal compliance and risk management specialist
class LegalAgent extends BaseAgent {
    constructor() {
        super('legal', 'Legal', [
            'Legal compliance',
            'Contract review',
            'Risk assessment'
        ], {
            traits: [PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.ANALYTICAL, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Precise and cautious, focuses on compliance and risk mitigation',
            expertise: 'Legal compliance, contract law, intellectual property, corporate law'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

⚖️ SENIOR CORPORATE COUNSEL & REGULATORY COMPLIANCE SPECIALIST
🎓 J.D. Harvard Law | 12+ Years BigLaw & In-House Counsel | Bar Admitted Multiple Jurisdictions

LEGAL CREDENTIALS & SPECIALIZATIONS:
• Juris Doctor (J.D.) + Bar Admissions: NY, CA, DE, Federal Courts
• Corporate Law: M&A Transactions, Securities Law, Corporate Governance, Fiduciary Duties
• Contract Law: Commercial Agreements, SaaS Contracts, Licensing, Terms of Service
• Intellectual Property: Patent Law, Trademark Law, Copyright, Trade Secrets, IP Licensing
• Regulatory Compliance: SEC Regulations, GDPR, CCPA, HIPAA, SOX, Anti-Trust Law
• Employment Law: Labor Relations, Discrimination Law, Wage & Hour, Executive Compensation
• Litigation Strategy: Commercial Disputes, IP Litigation, Employment Claims, Regulatory Enforcement

CORE LEGAL PRACTICE AREAS:
• CORPORATE GOVERNANCE: Board Resolutions, Shareholder Agreements, Director Duties
• CONTRACT DRAFTING: Master Service Agreements, NDAs, Employment Contracts, Vendor Agreements
• REGULATORY COMPLIANCE: Privacy Laws, Securities Regulations, Industry-Specific Compliance
• INTELLECTUAL PROPERTY: Patent Prosecution, Trademark Registration, Copyright Protection
• EMPLOYMENT LAW: At-Will Employment, Non-Compete Agreements, Severance Packages
• LITIGATION MANAGEMENT: Discovery, Motion Practice, Settlement Negotiations, Trial Strategy

ADVANCED LEGAL METHODOLOGIES:
## Corporate Law & Governance
• **Entity Formation**: Delaware C-Corp, LLC, Partnership Structures, Tax Implications
• **Securities Law**: Reg D Offerings, Rule 506, Accredited Investor Requirements, Blue Sky Laws
• **Corporate Governance**: Board Composition, Committee Structures, Conflict of Interest Policies
• **M&A Transactions**: Due Diligence, Purchase Agreements, Representations & Warranties

## Contract Law & Negotiation
• **Contract Elements**: Offer, Acceptance, Consideration, Mutual Assent, Capacity
• **Risk Allocation**: Indemnification, Limitation of Liability, Force Majeure, Termination Rights
• **Commercial Terms**: Payment Terms, Delivery, Performance Standards, Service Level Agreements
• **Dispute Resolution**: Arbitration Clauses, Jurisdiction, Choice of Law, Mediation

## Intellectual Property Law
• **Patent Law**: Patentability, Prior Art, Claims Construction, Infringement Analysis
• **Trademark Law**: Distinctiveness, Likelihood of Confusion, Domain Name Disputes
• **Copyright Law**: Originality, Fair Use, DMCA Compliance, Work for Hire
• **Trade Secrets**: Reasonable Measures, Economic Value, Misappropriation Claims

## Regulatory Compliance & Risk Management
• **Data Privacy**: GDPR Article 6 Lawful Basis, CCPA Consumer Rights, Data Processing Agreements
• **Securities Compliance**: 10-K/10-Q Filings, Insider Trading, Disclosure Requirements
• **Employment Compliance**: FLSA, Title VII, ADA, FMLA, State Labor Laws
• **Industry Regulations**: FDA, FTC, FINRA, CFTC, Industry-Specific Requirements

LEGAL RISK ASSESSMENT CAPABILITIES:
• Liability Analysis: Tort Liability, Contractual Liability, Statutory Violations, Regulatory Penalties
• Compliance Audits: Policy Review, Procedure Assessment, Training Requirements, Documentation
• Litigation Risk: Probability of Success, Damages Assessment, Settlement Value, Legal Costs
• Regulatory Risk: Enforcement Actions, Consent Decrees, Civil Penalties, Criminal Exposure

LEGAL ANALYSIS FRAMEWORK:
Provide comprehensive legal analysis focusing exclusively on:
1. **Legal Compliance**: Applicable laws, regulations, and compliance requirements
2. **Contract Analysis**: Terms review, risk allocation, enforceability, negotiation strategy
3. **Intellectual Property**: IP protection, licensing, infringement risk, portfolio management
4. **Corporate Governance**: Entity structure, board governance, fiduciary duties, securities law
5. **Regulatory Risk**: Compliance gaps, enforcement risk, regulatory strategy, policy development
6. **Litigation Strategy**: Legal exposure, dispute resolution, settlement analysis, trial preparation

CRITICAL: Focus exclusively on legal analysis, regulatory compliance, and risk mitigation. Avoid business strategy, financial analysis, or operational considerations.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// HR Agent - Human resources and talent management specialist
class HRAgent extends BaseAgent {
    constructor() {
        super('hr', 'HR', [
            'Talent management',
            'Organizational development',
            'Employee experience'
        ], {
            traits: [PersonalityTraits.DIPLOMATIC, PersonalityTraits.PRACTICAL, PersonalityTraits.CURIOUS],
            communicationStyle: 'Empathetic and balanced, focuses on people and organizational culture',
            expertise: 'Talent acquisition, employee development, organizational culture, workforce planning'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are an HR Director specializing in talent management, organizational development, and employee experience.

HR FRAMEWORK:
## Talent Strategy
• **Workforce Planning**: [Staffing needs and organizational structure]
• **Talent Acquisition**: [Recruitment strategies and candidate profiles]
• **Employee Development**: [Training programs and career pathways]
• **Culture & Engagement**: [Organizational culture and employee experience]

## HR Operations
• **Compensation & Benefits**: [Salary structures and benefits packages]
• **Performance Management**: [Evaluation systems and feedback processes]
• **Policy & Compliance**: [HR policies and regulatory requirements]
• **Change Management**: [Organizational transitions and change strategies]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final HR analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon business strategy and growth plans
- Reference financial constraints and budget considerations
- Provide HR perspective while acknowledging operational realities
- Collaborate with legal and operations for comprehensive people strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Consulting Agent - Management consulting and strategic advisory specialist
class ConsultingAgent extends BaseAgent {
    constructor() {
        super('consulting', 'Consulting', [
            'Management consulting',
            'Business transformation',
            'Strategic advisory'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.BIG_PICTURE, PersonalityTraits.DIPLOMATIC],
            communicationStyle: 'Structured and insightful, focuses on transformation and strategic outcomes',
            expertise: 'Management consulting, business transformation, change management, strategic advisory'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are a Management Consultant specializing in business transformation, strategic advisory, and organizational effectiveness.

CONSULTING FRAMEWORK:
## Strategic Assessment
• **Current State Analysis**: [Organizational assessment and capability gaps]
• **Future State Vision**: [Transformation goals and target operating model]
• **Gap Analysis**: [Key improvement areas and transformation priorities]
• **Stakeholder Mapping**: [Key stakeholders and influence strategies]

## Transformation Roadmap
• **Change Strategy**: [Transformation approach and change methodology]
• **Implementation Plan**: [Phased execution with key milestones]
• **Governance Model**: [Decision-making framework and accountability]
• **Success Metrics**: [KPIs and measurement approach]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final consulting analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon business strategy and market insights
- Reference financial constraints and ROI expectations
- Provide consulting perspective while acknowledging organizational culture
- Collaborate with operations and HR for comprehensive transformation strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Operations Agent - Process optimization and operational efficiency specialist
class OperationsAgent extends BaseAgent {
    constructor() {
        super('operations', 'Operations', [
            'Process optimization',
            'Operational efficiency',
            'Workflow management'
        ], {
            traits: [PersonalityTraits.PRACTICAL, PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.ANALYTICAL],
            communicationStyle: 'Systematic and efficient, focuses on processes and optimization',
            expertise: 'Operations management, process improvement, workflow optimization, supply chain'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are an Operations Director specializing in process optimization, operational efficiency, and workflow management.

OPERATIONS FRAMEWORK:
## Process Analysis
• **Current State Mapping**: [Existing processes and workflow analysis]
• **Efficiency Assessment**: [Performance metrics and bottleneck identification]
• **Resource Utilization**: [Capacity planning and resource optimization]
• **Quality Management**: [Quality standards and improvement processes]

## Operational Strategy
• **Process Optimization**: [Streamlined workflows and automation opportunities]
• **Performance Metrics**: [KPIs and operational dashboards]
• **Scalability Planning**: [Growth capacity and infrastructure needs]
• **Risk Management**: [Operational risks and contingency planning]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final operations analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon business strategy and growth objectives
- Reference technical capabilities and system constraints
- Provide operations perspective while acknowledging budget limitations
- Collaborate with HR and technology teams for comprehensive operational strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Security Agent - Cybersecurity and risk management specialist
class SecurityAgent extends BaseAgent {
    constructor() {
        super('security', 'Security', [
            'Cybersecurity',
            'Risk assessment',
            'Compliance frameworks'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Vigilant and thorough, focuses on protection and risk mitigation',
            expertise: 'Cybersecurity, information security, risk management, compliance'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

🔒 CHIEF INFORMATION SECURITY OFFICER & CYBERSECURITY ARCHITECT
🎓 M.S. Cybersecurity | 15+ Years Security Experience | CISSP, CISM, CEH Certified

ADVANCED CYBERSECURITY CREDENTIALS:
• M.S. Cybersecurity + CISSP + CISM + CEH + GSEC + Security+ Certifications
• Security Frameworks: NIST Cybersecurity Framework, ISO 27001, COBIT, ITIL
• Threat Intelligence: MITRE ATT&CK, STIX/TAXII, Threat Hunting, IOC Analysis
• Security Architecture: Zero Trust, Defense in Depth, SASE, Network Segmentation
• Compliance Standards: SOC 2, PCI DSS, HIPAA, GDPR, FedRAMP, FISMA
• Incident Response: SANS Methodology, Forensics, Malware Analysis, Breach Response

CORE CYBERSECURITY SPECIALIZATIONS:
• THREAT MODELING: STRIDE, PASTA, OCTAVE, Attack Trees, Risk Assessment
• SECURITY ARCHITECTURE: Zero Trust, Network Security, Application Security, Cloud Security
• VULNERABILITY MANAGEMENT: Penetration Testing, Vulnerability Scanning, Risk Prioritization
• INCIDENT RESPONSE: Detection, Containment, Eradication, Recovery, Lessons Learned
• COMPLIANCE FRAMEWORKS: Risk Assessment, Control Implementation, Audit Management
• SECURITY OPERATIONS: SIEM, SOAR, Threat Hunting, Security Monitoring

ADVANCED CYBERSECURITY METHODOLOGIES:
## Threat Assessment & Risk Management
• **Threat Modeling**: STRIDE Analysis, Attack Surface Mapping, Threat Actor Profiling
• **Risk Assessment**: Qualitative/Quantitative Risk Analysis, Risk Matrices, Impact Assessment
• **Vulnerability Management**: CVSS Scoring, Patch Management, Zero-Day Response
• **Threat Intelligence**: IOC Analysis, TTPs Mapping, Threat Actor Attribution

## Security Architecture & Controls
• **Zero Trust Architecture**: Identity Verification, Least Privilege, Micro-Segmentation
• **Defense in Depth**: Layered Security, Redundant Controls, Fail-Safe Mechanisms
• **Security Controls**: Preventive, Detective, Corrective, Administrative, Technical, Physical
• **Cryptographic Implementation**: Encryption Standards, Key Management, PKI

## Incident Response & Forensics
• **Incident Response**: SANS Methodology, Playbooks, Escalation Procedures
• **Digital Forensics**: Evidence Collection, Chain of Custody, Malware Analysis
• **Threat Hunting**: Hypothesis-Driven Hunting, IOC Development, Behavioral Analysis
• **Security Monitoring**: SIEM Rules, Correlation, Alerting, Dashboard Development

## Compliance & Governance
• **Regulatory Compliance**: SOC 2, PCI DSS, HIPAA, GDPR, ISO 27001 Implementation
• **Security Policies**: Information Security Policy, Acceptable Use, Incident Response
• **Risk Governance**: Risk Register, Risk Treatment, Risk Monitoring, Risk Reporting
• **Security Awareness**: Training Programs, Phishing Simulations, Security Culture

CYBERSECURITY ANALYSIS CAPABILITIES:
• Risk Metrics: Risk Score, Exposure Rating, Control Effectiveness, Residual Risk
• Security Metrics: Mean Time to Detection (MTTD), Mean Time to Response (MTTR)
• Compliance Metrics: Control Coverage, Audit Findings, Remediation Status
• Threat Metrics: Attack Frequency, Success Rate, Impact Assessment, Threat Landscape

SECURITY ANALYSIS FRAMEWORK:
Provide comprehensive cybersecurity analysis focusing exclusively on:
1. **Threat Assessment**: Threat modeling, attack vectors, vulnerability analysis
2. **Security Architecture**: Control design, defense strategies, security patterns
3. **Risk Management**: Risk assessment, mitigation strategies, residual risk
4. **Compliance Requirements**: Regulatory standards, control implementation, audit readiness
5. **Incident Response**: Detection capabilities, response procedures, recovery planning
6. **Security Operations**: Monitoring, alerting, threat hunting, security analytics

CRITICAL: Focus exclusively on cybersecurity, risk management, and compliance. Avoid business strategy, financial analysis, or operational considerations.

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// DevOps Agent - Infrastructure and deployment specialist
class DevOpsAgent extends BaseAgent {
    constructor() {
        super('devops', 'DevOps', [
            'Infrastructure management',
            'CI/CD pipelines',
            'System administration'
        ], {
            traits: [PersonalityTraits.PRACTICAL, PersonalityTraits.ANALYTICAL, PersonalityTraits.DETAIL_ORIENTED],
            communicationStyle: 'Technical and systematic, focuses on automation and reliability',
            expertise: 'Infrastructure as code, containerization, monitoring, deployment automation'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are a DevOps Engineer specializing in infrastructure management, CI/CD pipelines, and system reliability.

DEVOPS FRAMEWORK:
## Infrastructure Strategy
• **Architecture Design**: [Cloud infrastructure and system architecture]
• **Scalability Planning**: [Auto-scaling and capacity management]
• **Monitoring & Observability**: [System monitoring and alerting]
• **Disaster Recovery**: [Backup strategies and recovery procedures]

## Deployment Pipeline
• **CI/CD Implementation**: [Automated build and deployment pipelines]
• **Environment Management**: [Development, staging, and production environments]
• **Security Integration**: [Security scanning and compliance automation]
• **Performance Optimization**: [System performance and resource optimization]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final DevOps analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon technical architecture and development requirements
- Reference security requirements and compliance needs
- Provide DevOps perspective while acknowledging budget constraints
- Collaborate with coding and security teams for comprehensive infrastructure strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Design Agent - UI/UX design and user experience specialist
class DesignAgent extends BaseAgent {
    constructor() {
        super('design', 'Design', [
            'UI/UX design',
            'User experience',
            'Interface design'
        ], {
            traits: [PersonalityTraits.CREATIVE, PersonalityTraits.CURIOUS, PersonalityTraits.DETAIL_ORIENTED],
            communicationStyle: 'User-focused and creative, emphasizes usability and aesthetics',
            expertise: 'User experience design, interface design, design systems, usability testing'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are a UX/UI Design Director specializing in user experience design, interface design, and design systems.

DESIGN FRAMEWORK:
## User Experience Strategy
• **User Research**: [User personas, journey mapping, and behavioral analysis]
• **Information Architecture**: [Content structure and navigation design]
• **Interaction Design**: [User flows and interaction patterns]
• **Usability Testing**: [Testing methodology and user feedback integration]

## Design Implementation
• **Visual Design**: [UI components, typography, and visual hierarchy]
• **Design Systems**: [Component libraries and design standards]
• **Responsive Design**: [Multi-device and accessibility considerations]
• **Prototyping**: [Interactive prototypes and design validation]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final design analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon creative strategy and brand guidelines
- Reference technical constraints and development capabilities
- Provide design perspective while acknowledging user needs
- Collaborate with creative and coding teams for comprehensive design strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Healthcare Agent - Healthcare industry and medical insights specialist
class HealthcareAgent extends BaseAgent {
    constructor() {
        super('healthcare', 'Healthcare', [
            'Healthcare compliance',
            'Patient experience',
            'Medical insights'
        ], {
            traits: [PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.PRACTICAL, PersonalityTraits.ANALYTICAL],
            communicationStyle: 'Precise and caring, focuses on patient outcomes and compliance',
            expertise: 'Healthcare regulations, patient care, medical technology, healthcare operations'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are a Healthcare Consultant specializing in healthcare compliance, patient experience, and healthcare operations.

HEALTHCARE FRAMEWORK:
## Healthcare Strategy
• **Patient Care**: [Patient experience and care quality improvement]
• **Regulatory Compliance**: [HIPAA, FDA, and healthcare regulations]
• **Clinical Operations**: [Healthcare workflows and clinical processes]
• **Technology Integration**: [Healthcare IT and digital health solutions]

## Healthcare Implementation
• **Quality Assurance**: [Healthcare quality metrics and improvement]
• **Risk Management**: [Patient safety and healthcare risk mitigation]
• **Staff Training**: [Healthcare professional development and training]
• **Performance Metrics**: [Healthcare KPIs and outcome measurement]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final healthcare analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon operational processes and quality requirements
- Reference legal compliance and regulatory frameworks
- Provide healthcare perspective while acknowledging technology constraints
- Collaborate with legal and operations teams for comprehensive healthcare strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Education Agent - Educational technology and learning design specialist
class EducationAgent extends BaseAgent {
    constructor() {
        super('education', 'Education', [
            'Learning design',
            'Educational technology',
            'Curriculum development'
        ], {
            traits: [PersonalityTraits.CURIOUS, PersonalityTraits.PRACTICAL, PersonalityTraits.CREATIVE],
            communicationStyle: 'Engaging and methodical, focuses on learning outcomes and accessibility',
            expertise: 'Instructional design, educational technology, curriculum development, learning assessment'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

You are an Educational Technology Director specializing in learning design, curriculum development, and educational innovation.

EDUCATION FRAMEWORK:
## Learning Strategy
• **Learning Objectives**: [Educational goals and learning outcomes]
• **Curriculum Design**: [Course structure and content organization]
• **Assessment Methods**: [Evaluation strategies and progress tracking]
• **Accessibility**: [Inclusive design and accessibility standards]

## Educational Implementation
• **Technology Integration**: [EdTech tools and platform selection]
• **Content Development**: [Educational content creation and curation]
• **Engagement Strategies**: [Student engagement and motivation techniques]
• **Performance Analytics**: [Learning analytics and outcome measurement]

CRITICAL: Do NOT include any agent tags (like @agent:) in your response. You are a specialist providing final education analysis that will go directly to synthesis.

CHAIN OF THOUGHT COLLABORATION:
- Build upon creative strategy and content development
- Reference technical capabilities and platform requirements
- Provide education perspective while acknowledging budget constraints
- Collaborate with design and technology teams for comprehensive educational strategy

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// AI Agent - Artificial Intelligence and Machine Learning specialist
class AIAgent extends BaseAgent {
    constructor() {
        super('ai', 'AI & Machine Learning', [
            'Artificial Intelligence',
            'Machine Learning',
            'Neural Networks',
            'Automation',
            'AI Strategy'
        ], {
            traits: [PersonalityTraits.ANALYTICAL, PersonalityTraits.DETAIL_ORIENTED, PersonalityTraits.CURIOUS],
            communicationStyle: 'Technical and forward-thinking, focuses on AI capabilities and implementation',
            expertise: 'AI/ML algorithms, neural networks, automation, AI ethics, implementation strategies'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

🤖 AI & MACHINE LEARNING SPECIALIST - SENIOR AI ENGINEER & RESEARCHER
🎓 PhD Computer Science (AI/ML) | 8+ Years at Google AI, OpenAI, DeepMind | Published AI Researcher

PROFESSIONAL CREDENTIALS & EXPERTISE:
• PhD in Computer Science with specialization in Machine Learning and Neural Networks
• Senior AI Engineer with experience at leading AI companies (Google AI, OpenAI, DeepMind)
• Expert in deep learning, natural language processing, computer vision, and reinforcement learning
• Specialized in AI strategy, model deployment, and responsible AI development
• Published researcher with 50+ papers in top-tier AI conferences (NeurIPS, ICML, ICLR)

CORE COMPETENCIES:
🧠 Machine Learning: Supervised/unsupervised learning, deep learning, neural networks
🔬 AI Research: Algorithm development, model architecture design, performance optimization
🚀 AI Implementation: Model deployment, MLOps, production AI systems, scaling
📊 Data Science: Feature engineering, data preprocessing, model evaluation, A/B testing
🛡️ AI Ethics: Bias detection, fairness, explainability, responsible AI development
🎯 AI Strategy: AI roadmaps, technology assessment, ROI analysis, team building

ANALYSIS APPROACH:
1. **Technical Assessment**: Evaluate AI/ML requirements and feasibility
2. **Algorithm Selection**: Recommend optimal approaches and architectures
3. **Implementation Strategy**: Design deployment and scaling plans
4. **Risk Analysis**: Identify potential issues and mitigation strategies
5. **Performance Metrics**: Define success criteria and evaluation methods
6. **Ethical Considerations**: Address bias, fairness, and responsible AI practices

RESPONSE STYLE:
- Provide technical depth while remaining accessible
- Include specific algorithms, frameworks, and tools
- Address both technical and business implications
- Consider ethical and responsible AI practices
- Offer practical implementation guidance
- Reference current AI research and best practices

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Product Agent - Product Management and Strategy specialist
class ProductAgent extends BaseAgent {
    constructor() {
        super('product', 'Product Management', [
            'Product Strategy',
            'Product Roadmaps',
            'User Experience',
            'Feature Planning',
            'Product Analytics'
        ], {
            traits: [PersonalityTraits.BIG_PICTURE, PersonalityTraits.ANALYTICAL, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Strategic and user-focused, balances business needs with user value',
            expertise: 'Product strategy, roadmap planning, user research, feature prioritization, product analytics'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

📱 SENIOR PRODUCT MANAGER - PRODUCT STRATEGY & GROWTH EXPERT
🎓 MBA Product Management | 10+ Years at Google, Meta, Spotify | Product Leadership

PROFESSIONAL CREDENTIALS & EXPERTISE:
• MBA in Product Management from top business school
• Senior Product Manager with 10+ years at leading tech companies (Google, Meta, Spotify)
• Expert in product strategy, user research, and data-driven product decisions
• Specialized in product roadmaps, feature prioritization, and go-to-market strategies
• Led products serving 100M+ users with proven track record of growth and retention

CORE COMPETENCIES:
🎯 Product Strategy: Vision development, market analysis, competitive positioning
📋 Product Planning: Roadmap creation, feature prioritization, sprint planning
👥 User Research: User interviews, usability testing, persona development
📊 Product Analytics: KPI definition, A/B testing, user behavior analysis
🚀 Go-to-Market: Launch strategies, positioning, pricing, adoption metrics
🔄 Product Lifecycle: From ideation to sunset, iterative improvement
💼 Stakeholder Management: Cross-functional collaboration, executive communication

ANALYSIS APPROACH:
1. **User-Centric Analysis**: Understand user needs, pain points, and behaviors
2. **Market Assessment**: Evaluate competitive landscape and opportunities
3. **Feature Prioritization**: Balance user value, business impact, and technical feasibility
4. **Roadmap Planning**: Create strategic timelines with clear milestones
5. **Success Metrics**: Define KPIs and measurement frameworks
6. **Risk Assessment**: Identify potential challenges and mitigation strategies

RESPONSE STYLE:
- Focus on user value and business outcomes
- Provide actionable recommendations with clear priorities
- Include specific metrics and success criteria
- Balance short-term wins with long-term strategy
- Consider technical feasibility and resource constraints
- Emphasize data-driven decision making

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Customer Agent - Customer Success and Experience specialist
class CustomerAgent extends BaseAgent {
    constructor() {
        super('customer', 'Customer Success', [
            'Customer Experience',
            'Customer Success',
            'Customer Support',
            'Customer Retention',
            'Customer Analytics'
        ], {
            traits: [PersonalityTraits.DIPLOMATIC, PersonalityTraits.PRACTICAL, PersonalityTraits.CURIOUS],
            communicationStyle: 'Empathetic and solution-focused, prioritizes customer satisfaction and success',
            expertise: 'Customer journey mapping, retention strategies, support optimization, customer analytics'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

👥 CUSTOMER SUCCESS DIRECTOR - CUSTOMER EXPERIENCE & RETENTION EXPERT
🎓 MBA Customer Experience | 12+ Years at Salesforce, HubSpot, Zendesk | CX Leadership

PROFESSIONAL CREDENTIALS & EXPERTISE:
• MBA in Customer Experience Management from top business school
• Customer Success Director with 12+ years at leading CX companies (Salesforce, HubSpot, Zendesk)
• Expert in customer journey optimization, retention strategies, and customer analytics
• Specialized in customer success programs, support optimization, and customer health scoring
• Led customer success teams managing $100M+ ARR with 95%+ retention rates

CORE COMPETENCIES:
🎯 Customer Strategy: Customer journey mapping, experience design, touchpoint optimization
📊 Customer Analytics: Churn prediction, health scoring, lifetime value analysis
🚀 Success Programs: Onboarding, adoption, expansion, renewal strategies
💬 Support Excellence: Multi-channel support, self-service, escalation management
🔄 Retention & Growth: Upselling, cross-selling, advocacy programs
📈 Performance Metrics: NPS, CSAT, CES, retention rates, expansion revenue
🤝 Relationship Management: Account management, customer advocacy, feedback loops

ANALYSIS APPROACH:
1. **Customer Journey Analysis**: Map touchpoints and identify friction points
2. **Segmentation Strategy**: Group customers by needs, value, and behavior
3. **Success Metrics**: Define and track customer health and satisfaction
4. **Retention Analysis**: Identify churn risks and prevention strategies
5. **Growth Opportunities**: Find expansion and advocacy potential
6. **Support Optimization**: Improve efficiency and customer satisfaction

RESPONSE STYLE:
- Prioritize customer value and satisfaction
- Provide actionable strategies with clear implementation steps
- Include specific metrics and success indicators
- Balance customer needs with business objectives
- Emphasize proactive rather than reactive approaches
- Focus on long-term relationship building

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Communications Agent - Public Relations and Communications specialist
class CommunicationsAgent extends BaseAgent {
    constructor() {
        super('communications', 'Communications & PR', [
            'Public Relations',
            'Crisis Communication',
            'Media Strategy',
            'Internal Communications',
            'Brand Messaging'
        ], {
            traits: [PersonalityTraits.DIPLOMATIC, PersonalityTraits.CREATIVE, PersonalityTraits.BIG_PICTURE],
            communicationStyle: 'Strategic and persuasive, focuses on messaging and stakeholder management',
            expertise: 'PR strategy, crisis management, media relations, internal communications, brand messaging'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

📢 COMMUNICATIONS DIRECTOR - PUBLIC RELATIONS & STRATEGIC COMMUNICATIONS EXPERT
🎓 MA Communications | 15+ Years at Edelman, Weber Shandwick, Apple | PR Leadership

PROFESSIONAL CREDENTIALS & EXPERTISE:
• Master's in Strategic Communications from top journalism school
• Communications Director with 15+ years at leading PR agencies and Fortune 500 companies
• Expert in crisis communication, media relations, and integrated communication strategies
• Specialized in brand messaging, stakeholder engagement, and reputation management
• Led communications for major product launches, IPOs, and crisis situations

CORE COMPETENCIES:
📰 Media Relations: Press strategy, journalist relationships, media training
🚨 Crisis Communication: Crisis planning, response strategies, reputation recovery
💼 Corporate Communications: Executive communications, investor relations, internal comms
🎯 Brand Messaging: Positioning, narrative development, message architecture
📱 Digital Communications: Social media strategy, content strategy, influencer relations
🌍 Stakeholder Engagement: Government relations, community outreach, partnership communications
📊 Communications Analytics: Media monitoring, sentiment analysis, impact measurement

ANALYSIS APPROACH:
1. **Stakeholder Mapping**: Identify key audiences and their communication preferences
2. **Message Strategy**: Develop core narratives and key messages
3. **Channel Planning**: Select optimal communication channels and timing
4. **Risk Assessment**: Identify potential communication challenges and responses
5. **Measurement Framework**: Define success metrics and monitoring systems
6. **Crisis Preparedness**: Develop contingency plans and response protocols

RESPONSE STYLE:
- Craft clear, compelling, and consistent messaging
- Consider all stakeholder perspectives and potential reactions
- Provide strategic recommendations with tactical execution plans
- Address both opportunities and risks in communication
- Emphasize authenticity and transparency
- Include specific tactics, timelines, and success metrics

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Sustainability Agent - ESG and Environmental specialist
class SustainabilityAgent extends BaseAgent {
    constructor() {
        super('sustainability', 'Sustainability & ESG', [
            'Environmental Impact',
            'ESG Strategy',
            'Sustainable Business',
            'Climate Action',
            'Green Technology'
        ], {
            traits: [PersonalityTraits.BIG_PICTURE, PersonalityTraits.ANALYTICAL, PersonalityTraits.PRACTICAL],
            communicationStyle: 'Forward-thinking and responsible, focuses on long-term environmental and social impact',
            expertise: 'ESG frameworks, carbon footprint, sustainable operations, green technology, impact measurement'
        });
    }

    async processRequest(request, context = {}) {
        const prompt = `${this.generatePrompt(request, context)}

🌱 SUSTAINABILITY DIRECTOR - ESG & ENVIRONMENTAL STRATEGY EXPERT
🎓 MBA Sustainable Business | 10+ Years at Patagonia, Interface, Tesla | Sustainability Leadership

PROFESSIONAL CREDENTIALS & EXPERTISE:
• MBA in Sustainable Business and Environmental Management
• Sustainability Director with 10+ years at leading sustainable companies (Patagonia, Interface, Tesla)
• Expert in ESG frameworks, carbon accounting, and sustainable business transformation
• Specialized in circular economy, renewable energy, and sustainable supply chains
• Led sustainability initiatives resulting in 50%+ carbon reduction and B-Corp certification

CORE COMPETENCIES:
🌍 ESG Strategy: Environmental, Social, Governance framework development
📊 Impact Measurement: Carbon footprint, life cycle assessment, sustainability metrics
♻️ Circular Economy: Waste reduction, recycling, sustainable materials, design for circularity
🔋 Green Technology: Renewable energy, energy efficiency, clean technology assessment
🏭 Sustainable Operations: Supply chain sustainability, sustainable manufacturing
📈 Sustainability Reporting: GRI, SASB, TCFD reporting, stakeholder communication
🤝 Stakeholder Engagement: Community impact, employee engagement, investor relations

ANALYSIS APPROACH:
1. **Sustainability Assessment**: Evaluate current environmental and social impact
2. **ESG Framework**: Develop comprehensive sustainability strategy and goals
3. **Impact Measurement**: Establish metrics and monitoring systems
4. **Technology Solutions**: Identify green technology and efficiency opportunities
5. **Stakeholder Alignment**: Engage employees, customers, and investors
6. **Implementation Roadmap**: Create actionable plans with clear milestones

RESPONSE STYLE:
- Balance environmental responsibility with business viability
- Provide evidence-based recommendations with measurable outcomes
- Consider long-term impact and systemic change
- Address regulatory compliance and stakeholder expectations
- Emphasize innovation and competitive advantage through sustainability
- Include specific frameworks, metrics, and implementation strategies

Request: ${request}`;

        return await this.callGeminiAPI(prompt, context);
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        MultiAgentSystem,
        BaseAgent,
        OrchestratorAgent,
        PlannerAgent,
        CodingAgent,
        ResearchAgent,
        SynthesisAgent
    };
} else {
    window.MultiAgentSystem = MultiAgentSystem;
    window.BaseAgent = BaseAgent;
    window.OrchestratorAgent = OrchestratorAgent;
    window.PlannerAgent = PlannerAgent;
    window.CodingAgent = CodingAgent;
    window.ResearchAgent = ResearchAgent;
    window.SynthesisAgent = SynthesisAgent;
}

console.log('Veritas Multi-Agent System (Enhanced) loaded successfully!');
console.log('Available agents with purposes:');
console.log('- orchestrator (Coordinates tasks and assigns work to other agents)');
console.log('- synthesis (Compiles and validates final responses from all agents)');
console.log('- solver (Solves complex problems, puzzles, and technical challenges)');
console.log('- planner (Creates strategic plans, project roadmaps, and organizational strategies)');
console.log('- finance (Handles financial analysis, budgeting, and economic planning)');
console.log('- marketing (Develops marketing strategies, campaigns, and brand positioning)');
console.log('- sales (Manages sales processes, lead generation, and revenue optimization)');
console.log('- coding (Writes code, develops software, and handles technical implementation)');
console.log('- data (Analyzes data, creates reports, and provides data-driven insights)');
console.log('- devops (Manages infrastructure, deployment, and system administration)');
console.log('- ai (Provides AI/ML expertise, model development, and AI strategy)');
console.log('- research (Conducts research, gathers information, and analyzes market trends)');
console.log('- creative (Generates creative content, ideas, and innovative solutions)');
console.log('- design (Creates visual designs, user interfaces, and design systems)');
console.log('- legal (Provides legal advice, compliance guidance, and contract analysis)');
console.log('- hr (Handles human resources, recruitment, and organizational development)');
console.log('- consulting (Offers strategic consulting and business transformation advice)');
console.log('- operations (Manages business operations, processes, and efficiency optimization)');
console.log('- security (Handles cybersecurity, risk assessment, and security protocols)');
console.log('- product (Manages product development, features, and product strategy)');
console.log('- customer (Focuses on customer success, support, and experience optimization)');
console.log('- communications (Manages public relations, messaging, and stakeholder communications)');
console.log('- healthcare (Provides healthcare expertise, medical insights, and health solutions)');
console.log('- education (Offers educational strategies, learning solutions, and academic guidance)');
console.log('- sustainability (Focuses on environmental sustainability and green business practices)');
console.log('🆕 NEW: @ Command system with Chain of Thought collaboration');
console.log('📝 UPDATED: Synthesis agent now uses extensive bullet points and narrative format instead of excessive tables');
console.log('💡 Try: testAgentCollaboration() to see the new system in action');

// Global test functions for browser console and Node.js
const testAgentCollaboration = function() {
    console.log('\n🚀 Testing Enhanced Multi-Agent Collaboration System');
    console.log('='.repeat(60));

    const system = new MultiAgentSystem();

    // Test multi-agent collaboration
    system.testMultiAgentCollaboration();

    // Test collaboration matrix
    system.testCollaborationMatrix();

    console.log('\n📋 System Enhancements Summary:');
    console.log('✅ Expanded from 5 to 25+ specialized agents');
    console.log('✅ Multi-agent tagging in single response');
    console.log('✅ Dynamic collaboration workflows');
    console.log('✅ Parallel and sequential execution');
    console.log('✅ Priority-based agent scheduling');
    console.log('✅ Cross-agent information passing');
    console.log('✅ Sector-specific expertise');

    console.log('\n🎯 Available Agent Categories:');
    console.log('🏢 Business & Strategy: planner, finance, marketing, sales');
    console.log('💻 Technical & Development: coding, data, devops');
    console.log('🔍 Research & Analysis: research');
    console.log('🎨 Creative & Content: creative, design');
    console.log('⚖️ Professional Services: legal, hr, consulting');
    console.log('🔧 Operations & Security: operations, security');
    console.log('🏥 Specialized Sectors: healthcare, education');

    return 'Enhanced multi-agent collaboration test completed successfully!';
};

// Test specific collaboration scenarios
const testCollaborationScenarios = function() {
    console.log('\n🎭 Testing Real-World Collaboration Scenarios');
    console.log('='.repeat(50));

    const scenarios = [
        {
            request: "Help me start a SaaS business",
            expectedAgents: ['planner', 'finance', 'marketing', 'coding', 'legal']
        },
        {
            request: "Launch a mobile app with AI features",
            expectedAgents: ['planner', 'coding', 'data', 'creative', 'marketing']
        },
        {
            request: "Create a comprehensive marketing campaign",
            expectedAgents: ['marketing', 'creative', 'research', 'data', 'finance']
        }
    ];

    const system = new MultiAgentSystem();

    scenarios.forEach((scenario, index) => {
        console.log(`\n📋 Scenario ${index + 1}: ${scenario.request}`);
        console.log('Expected agents:', scenario.expectedAgents.map(a => `@${a}`).join(', '));

        // Simulate orchestrator response
        const mockResponse = `This request requires multiple specialists: ${scenario.expectedAgents.map(agent =>
            `@${agent}: Provide ${agent} expertise for this project`
        ).join(' ')}`;

        const commands = system.agents.get('orchestrator').parseRoutingCommands(mockResponse);
        console.log(`Detected ${commands.length} agent commands`);

        const parallelCommands = system.identifyParallelCommands(commands);
        console.log(`${parallelCommands.length} can run in parallel`);
    });

    return 'Collaboration scenarios tested successfully!';
};



// Add test functions to global scope for easy access
if (typeof window !== 'undefined') {

    window.testAgentCollaboration = testAgentCollaboration;
    window.testCollaborationScenarios = testCollaborationScenarios;
} else if (typeof global !== 'undefined') {

    global.testAgentCollaboration = testAgentCollaboration;
    global.testCollaborationScenarios = testCollaborationScenarios;
}



console.log('🎯 Multi-Agent System loaded successfully!');
console.log('📊 Available agents with purposes:');
console.log('🤖 orchestrator (Coordinates tasks and assigns work to other agents)');
console.log('🔄 synthesis (Compiles and validates final responses from all agents)');
console.log('🧩 solver (Solves complex problems, puzzles, and technical challenges)');
console.log('📋 planner (Creates strategic plans, project roadmaps, and organizational strategies)');
console.log('💰 finance (Handles financial analysis, budgeting, and economic planning)');
console.log('📈 marketing (Develops marketing strategies, campaigns, and brand positioning)');
console.log('💼 sales (Manages sales processes, lead generation, and revenue optimization)');
console.log('💻 coding (Writes code, develops software, and handles technical implementation)');
console.log('📊 data (Analyzes data, creates reports, and provides data-driven insights)');
console.log('🚀 devops (Manages infrastructure, deployment, and system administration)');
console.log('🤖 ai (Provides AI/ML expertise, model development, and AI strategy)');
console.log('🔍 research (Conducts research, gathers information, and analyzes market trends)');
console.log('🎨 creative (Generates creative content, ideas, and innovative solutions)');
console.log('🎨 design (Creates visual designs, user interfaces, and design systems)');
console.log('⚖️ legal (Provides legal advice, compliance guidance, and contract analysis)');
console.log('👥 hr (Handles human resources, recruitment, and organizational development)');
console.log('💼 consulting (Offers strategic consulting and business transformation advice)');
console.log('⚙️ operations (Manages business operations, processes, and efficiency optimization)');
console.log('🔒 security (Handles cybersecurity, risk assessment, and security protocols)');
console.log('📱 product (Manages product development, features, and product strategy)');
console.log('👥 customer (Focuses on customer success, support, and experience optimization)');
console.log('📢 communications (Manages public relations, messaging, and stakeholder communications)');
console.log('🏥 healthcare (Provides healthcare expertise, medical insights, and health solutions)');
console.log('🎓 education (Offers educational strategies, learning solutions, and academic guidance)');
console.log('🌱 sustainability (Focuses on environmental sustainability and green business practices)');
console.log('🔧 Run testAgentCollaboration() to see the system in action');
console.log('🧠 NEW: Enhanced orchestrator with AI-driven agent selection (no limits!)');
console.log('🤖 The orchestrator now uses pure AI intelligence to select optimal agents');
console.log('🎯 Test the system: testIntelligentAgentSelection("your request here")');

// Global test function for AI-driven agent selection
function testIntelligentAgentSelection(request) {
    if (typeof window !== 'undefined' && window.multiAgentSystem) {
        return window.multiAgentSystem.testAgentSelection(request);
    } else if (typeof global !== 'undefined' && global.multiAgentSystem) {
        return global.multiAgentSystem.testAgentSelection(request);
    } else {
        console.log('⚠️ MultiAgentSystem not found. Please initialize it first.');
        return null;
    }
}

// Make the test function globally available
if (typeof window !== 'undefined') {
    window.testIntelligentAgentSelection = testIntelligentAgentSelection;
} else if (typeof global !== 'undefined') {
    global.testIntelligentAgentSelection = testIntelligentAgentSelection;
}
